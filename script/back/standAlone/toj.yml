version: "3"
services:

  business-toj-judge:
    image: registry.cn-guangzhou.aliyuncs.com/glowxq/business:business-toj-judge
    container_name: business-toj-judge
    restart: always
    volumes:
      - ${HOJ_DATA_DIRECTORY}/testcase:/judge/test_case
      - ${HOJ_DATA_DIRECTORY}/judge/log:/judge/log
      - ${HOJ_DATA_DIRECTORY}/judge/run:/judge/run
      - ${HOJ_DATA_DIRECTORY}/judge/spj:/judge/spj
      - ${HOJ_DATA_DIRECTORY}/judge/interactive:/judge/interactive
      - ${HOJ_DATA_DIRECTORY}/log/judgeserver:/judge/log/judgeserver
    environment:
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Xms192m -Xmx384m # 修正JVM参数以便适应单机部署
      - JUDGE_SERVER_IP=${JUDGE_SERVER_IP:-**********}
      - JUD<PERSON>_SERVER_PORT=${JUDGE_SERVER_PORT:-8203}
      - JUDGE_SERVER_NAME=${JUDGE_SERVER_NAME:-judger-alone} # 判题服务的名字
      - NACOS_URL=${NACOS_HOST:-*************}:${NACOS_PORT:-8848}
      - NACOS_USERNAME=${NACOS_USERNAME:-nacos}
      - NACOS_PASSWORD=${NACOS_PASSWORD:-nacos}
      - MAX_TASK_NUM=${MAX_TASK_NUM:--1} # -1表示最大可接收判题任务数为cpu核心数+1
      - REMOTE_JUDGE_OPEN=${REMOTE_JUDGE_OPEN:-true} # 当前判题服务器是否开启远程虚拟判题功能
      - REMOTE_JUDGE_MAX_TASK_NUM=${REMOTE_JUDGE_MAX_TASK_NUM:--1} # -1表示最大可接收远程判题任务数为cpu核心数*2+1
      - PARALLEL_TASK=${PARALLEL_TASK:-default} # 默认沙盒并行判题程序数为cpu核心数
    ports:
      - ${JUDGE_SERVER_PORT:-8203}:${JUDGE_SERVER_PORT:-8203}
      # - "0.0.0.0:5050:5050" # 一般不开放安全沙盒端口
    healthcheck:
      test: curl -f http://${JUDGE_SERVER_IP:-**********}:${JUDGE_SERVER_PORT:-8203}/version || exit 1
      interval: 30s
      timeout: 10s
      retries: 3
    privileged: true # 设置容器的权限为root
    shm_size: 512mb
    networks:
      hoj-network:
        ipv4_address: **********

  business_toj_api:
    image: registry.cn-guangzhou.aliyuncs.com/glowxq/business:business-toj-api
    container_name: business-toj-api
    #支持amd64、arm64
    restart: always
    volumes:
      - ${HOJ_DATA_DIRECTORY}/file:/hoj/file
      - ${HOJ_DATA_DIRECTORY}/testcase:/hoj/testcase
      - ${HOJ_DATA_DIRECTORY}/log/backend:/hoj/log/backend
    environment:
      - TZ=Asia/Shanghai
      - JAVA_OPTS=-Xms192m -Xmx384m
      - BACKEND_SERVER_PORT=${BACKEND_PORT:-8202}
      - NACOS_URL=${NACOS_HOST:-*************}:${NACOS_PORT:-8848}
      - NACOS_USERNAME=${NACOS_USERNAME:-nacos}
      - NACOS_PASSWORD=${NACOS_PASSWORD:-nacos}
      - JWT_TOKEN_SECRET=${JWT_TOKEN_SECRET:-default} # token加密秘钥 默认则生成32位随机密钥
      - JWT_TOKEN_EXPIRE=${JWT_TOKEN_EXPIRE:-86400} # token过期时间默认为24小时 86400s
      - JWT_TOKEN_FRESH_EXPIRE=${JWT_TOKEN_FRESH_EXPIRE:-43200} # token默认12小时可自动刷新
      - JUDGE_TOKEN=${JUDGE_TOKEN:-default} # 调用判题服务器的token 默认则生成32位随机密钥
      - MYSQL_HOST=${MYSQL_HOST:-*************}
      - MYSQL_PORT=${MYSQL_PORT:-3306}
      - MYSQL_PUBLIC_HOST=${MYSQL_PUBLIC_HOST:-*************} # 如果判题服务是分布式，请提供当前mysql所在服务器的公网ip
      - MYSQL_PUBLIC_PORT=${MYSQL_PUBLIC_PORT:-3306}
      - MYSQL_DATABASE_NAME=hoj # 改动需要修改hoj-mysql镜像,默认为hoj
      - MYSQL_USERNAME=root
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-Glowxq@123456}
      - EMAIL_SERVER_HOST=${EMAIL_SERVER_HOST:-smtp.qq.com} # 请使用邮件服务的域名或ip
      - EMAIL_SERVER_PORT=${EMAIL_SERVER_PORT:-465} # 请使用邮件服务的端口号
      - EMAIL_USERNAME=${EMAIL_USERNAME:-your_email_username} # 请使用对应邮箱账号
      - EMAIL_PASSWORD=${EMAIL_PASSWORD:-your_email_password} # 请使用对应邮箱密码
      - REDIS_HOST=${REDIS_HOST:-*************}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-Glowxq@123456}
      - OPEN_REMOTE_JUDGE=true # 是否开启各个remote judge
      # 开启虚拟判题请提供对应oj的账号密码 格式为 
      # username1,username2,...
      # password1,password2,...
      - HDU_ACCOUNT_USERNAME_LIST=${HDU_ACCOUNT_USERNAME_LIST}
      - HDU_ACCOUNT_PASSWORD_LIST=${HDU_ACCOUNT_PASSWORD_LIST}
      - CF_ACCOUNT_USERNAME_LIST=${CF_ACCOUNT_USERNAME_LIST}
      - CF_ACCOUNT_PASSWORD_LIST=${CF_ACCOUNT_PASSWORD_LIST}
      - POJ_ACCOUNT_USERNAME_LIST=${POJ_ACCOUNT_USERNAME_LIST}
      - POJ_ACCOUNT_PASSWORD_LIST=${POJ_ACCOUNT_PASSWORD_LIST}
      - ATCODER_ACCOUNT_USERNAME_LIST=${ATCODER_ACCOUNT_USERNAME_LIST}
      - ATCODER_ACCOUNT_PASSWORD_LIST=${ATCODER_ACCOUNT_PASSWORD_LIST}
      - SPOJ_ACCOUNT_USERNAME_LIST=${SPOJ_ACCOUNT_USERNAME_LIST}
      - SPOJ_ACCOUNT_PASSWORD_LIST=${SPOJ_ACCOUNT_PASSWORD_LIST}
      - LIBRE_ACCOUNT_USERNAME_LIST=${LIBRE_ACCOUNT_USERNAME_LIST}
      - LIBRE_ACCOUNT_PASSWORD_LIST=${LIBRE_ACCOUNT_PASSWORD_LIST}
      # 是否强制使用配置文件的remote judge账号覆盖原有系统的账号列表
      - FORCED_UPDATE_REMOTE_JUDGE_ACCOUNT=${FORCED_UPDATE_REMOTE_JUDGE_ACCOUNT:-false}
    ports:
      - ${BACKEND_PORT:-8202}:${BACKEND_PORT:-8202}


  hoj-autohealth:  # 监控不健康的容器进行重启
    restart: always
    container_name: hoj-autohealth
    image: willfarrell/autoheal
    environment:
      - AUTOHEAL_CONTAINER_LABEL=all
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    
networks:
   hoj-network:
     driver: bridge
     ipam:
       config:
         - subnet: ${SUBNET:-**********/16}