version: '3'
services:
  nacos:
    image: nacos/nacos-server:1.4.2
    container_name: nacos_server
    environment:
      MODE: standalone
      # 新增以下两行，强制绑定到 0.0.0.0
      NACOS_SERVER_IP: 0.0.0.0   # 关键修复点
      NACOS_APPLICATION_PORT: 8848  # 显式指定端口（可选）
      # 原有其他配置保持不变
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: rm-7xvweu4nb7jz1xuin.mysql.rds.aliyuncs.com
      MYSQL_SERVICE_PORT: "3306"
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: TuCO123456
      NACOS_AUTH_ENABLE: "true"
      JVM_XMS: 512m
      JVM_XMX: 1024m
    ports:
      - "8200:8848"  # 宿主机 8200 映射到容器 8848
    restart: always
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8848/nacos/" ]
      interval: 30s
      timeout: 5s
      retries: 5