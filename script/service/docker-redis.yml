version: '3.8'

services:
  redis:
    image: redis:7.0
    container_name: redis7
    ports:
      - "6379:6379"
    volumes:
      - /mnt/data/my/app/redis/data:/data
      - /mnt/data/my/app/redis/conf/redis.conf:/etc/redis/redis.conf
    command: >
      redis-server /etc/redis/redis.conf
      --appendonly yes
      --requirepass TuCO12345
      --tcp-keepalive 60
      --dir /data
    restart: always