user  nginx;
worker_processes  auto;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 设置全局请求体最大值为 512MB
    client_max_body_size 512M;

    # 增加缓冲区大小和超时时间
    client_body_buffer_size 128k;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # 代理相关配置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    proxy_buffer_size 4k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;

    # SSL 全局配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 证书路径定义
    ssl_certificate     /home/<USER>/cert/oj.aicx.cc.pem;
    ssl_certificate_key /home/<USER>/cert/oj.aicx.cc.key;

    # 网站展示服务 (8080)
    server {
        listen 8080 ssl;
        server_name oj.aicx.cc;

        # 禁止CONNECT方法
        if ($request_method = CONNECT) {
            return 405;
        }

        # 网站根目录（请修改为实际路径）
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }
    }

    # go-judge 接口服务 - 8211:8201 端口
    server {
        listen 8211 ssl;
        server_name oj.aicx.cc;
        client_max_body_size 512M;  # 显式覆盖

        # 禁止CONNECT方法
        if ($request_method = CONNECT) {
            return 405;
        }

        location / {
            proxy_pass http://localhost:8201;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            # 确保后端服务接收原始请求体大小
            proxy_set_header X-Original-Content-Length $request_length;
        }
    }

    # tuco-api 接口服务 - 8212:8202 端口
    server {
        listen 8212 ssl;
        server_name oj.aicx.cc;
        client_max_body_size 512M;  # 显式覆盖

        # 禁止CONNECT方法
        if ($request_method = CONNECT) {
            return 405;
        }

        location / {
            proxy_pass http://localhost:8202;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            # 确保后端服务接收原始请求体大小
            proxy_set_header X-Original-Content-Length $request_length;
        }
    }

    # tuco-jugde 接口服务 - 8213:8203 端口
    server {
        listen 8213 ssl;
        server_name oj.aicx.cc;
        client_max_body_size 512M;  # 显式覆盖

        # 禁止CONNECT方法
        if ($request_method = CONNECT) {
            return 405;
        }

        location / {
            proxy_pass http://localhost:8203;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            # 确保后端服务接收原始请求体大小
            proxy_set_header X-Original-Content-Length $request_length;
        }
    }
}