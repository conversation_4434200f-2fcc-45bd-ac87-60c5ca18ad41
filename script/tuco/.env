# .env 文件

# 基础配置
# hoj全部数据存储的文件夹位置（默认当前路径生成hoj文件夹）
ENV=prod
TUCO_DATA_DIRECTORY=/mnt/data/my/tuco/data
TZ=Asia/Shanghai

# Nacos 配置
NACOS_HOST=**************
NACOS_PORT=8200
NACOS_USERNAME=nacos
NACOS_PASSWORD=TuCO123456

# MySQL 配置
MYSQL_HOST=rm-7xvweu4nb7jz1xuin.mysql.rds.aliyuncs.com
MYSQL_PORT=3306
MYSQL_PUBLIC_HOST=rm-7xvweu4nb7jz1xuinno.mysql.rds.aliyuncs.com
MYSQL_PUBLIC_PORT=3306
MYSQL_ROOT_PASSWORD=TuCO123456

# Redis 配置
REDIS_HOST=r-7xvxtli474xudpuuc8pd.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_PUBLIC_HOST=r-7xvxtli474xudpuuc8pd.redis.rds.aliyuncs.com
REDIS_PUBLIC_PORT=6379
REDIS_PASSWORD=TuCO123456

# api 服务配置
API_HOST=***********
API_PORT=8202

# judge 判题服务配置
JUDGE_SERVER_IP=-1
JUDGE_SERVER_PORT=8203
JUDGE_SERVER_NAME=judger-alone
# -1表示可接收最大判题任务数为cpu核心数+1
MAX_TASK_NUM=-1
# 当前判题服务器是否开启远程虚拟判题功能
REMOTE_JUDGE_OPEN=true
# -1表示可接收最大远程判题任务数为cpu核心数*2+1
REMOTE_JUDGE_MAX_TASK_NUM=-1
# 默认沙盒并行判题程序数为cpu核心数
PARALLEL_TASK=default

# 安全配置
JWT_TOKEN_SECRET=default
# token过期时间默认为24小时 86400s
JWT_TOKEN_EXPIRE=86400
# token默认12小时可自动刷新
JWT_TOKEN_FRESH_EXPIRE=43200
# 调用判题服务器的token 默认则生成32位随机密钥
JUDGE_TOKEN=default


# 邮件服务配置
EMAIL_SERVER_HOST=smtp.qq.com
EMAIL_SERVER_PORT=465
EMAIL_USERNAME=your_email_username
EMAIL_PASSWORD=your_email_password

# 远程判题配置
# 开启虚拟判题请提供对应oj的账号密码 格式为
# username1,username2,...
# password1,password2,...
HDU_ACCOUNT_USERNAME_LIST=
HDU_ACCOUNT_PASSWORD_LIST=
CF_ACCOUNT_USERNAME_LIST=
CF_ACCOUNT_PASSWORD_LIST=
POJ_ACCOUNT_USERNAME_LIST=
POJ_ACCOUNT_PASSWORD_LIST=
ATCODER_ACCOUNT_USERNAME_LIST=
ATCODER_ACCOUNT_PASSWORD_LIST=
SPOJ_ACCOUNT_USERNAME_LIST=
SPOJ_ACCOUNT_PASSWORD_LIST=
LIBRE_ACCOUNT_USERNAME_LIST=
LIBRE_ACCOUNT_PASSWORD_LIST=
# 是否强制使用上面配置的账号覆盖系统原有的账号列表
FORCED_UPDATE_REMOTE_JUDGE_ACCOUNT=false

