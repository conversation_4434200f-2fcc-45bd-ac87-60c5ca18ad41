# Docker Compose 版本
version: "3.8"

# 服务定义
services:

  # 业务API服务
  business-tuco-api:
    image: registry.cn-guangzhou.aliyuncs.com/glowxq/business:business-tuco-api
    container_name: business-tuco-api
    restart: always
    volumes:
      - ${TUCO_DATA_DIRECTORY}/file:/hoj/file                  # 文件存储目录
      - ${TUCO_DATA_DIRECTORY}/testcase:/hoj/testcase           # 测试用例目录
      - ${TUCO_DATA_DIRECTORY}/log/backend:/hoj/log/backend     # 后端日志目录
    environment:
      # 环境设置
      - ENV=${ENV:-prod}
      - TZ=Asia/Shanghai                                      # 时区设置
      - JAVA_OPTS=-Xms1024m -Xmx2048m                          # JVM 内存配置
      - BACKEND_SERVER_PORT=${API_PORT:-8202}
      - NACOS_URL=${NACOS_HOST:-**************}:${NACOS_PORT:-8200}
      - NACOS_USERNAME=${NACOS_USERNAME:-nacos}
      - NACOS_PASSWORD=${NACOS_PASSWORD:-TuCO123456}
      - JWT_TOKEN_SECRET=${JWT_TOKEN_SECRET:-default} # token加密秘钥 默认则生成32位随机密钥
      - JWT_TOKEN_EXPIRE=${JWT_TOKEN_EXPIRE:-86400} # token过期时间默认为24小时 86400s
      - JWT_TOKEN_FRESH_EXPIRE=${JWT_TOKEN_FRESH_EXPIRE:-43200} # token默认12小时可自动刷新
      - JUDGE_TOKEN=${JUDGE_TOKEN:-default} # 调用判题服务器的token 默认则生成32位随机密钥
      - MYSQL_HOST=${MYSQL_HOST:-rm-7xvweu4nb7jz1xuin.mysql.rds.aliyuncs.com}
      - MYSQL_PORT=${MYSQL_PORT:-3306}
      - MYSQL_PUBLIC_HOST=${MYSQL_PUBLIC_HOST:-rm-7xvweu4nb7jz1xuinno.mysql.rds.aliyuncs.com} # 如果判题服务是分布式，请提供当前mysql所在服务器的公网ip
      - MYSQL_PUBLIC_PORT=${MYSQL_PUBLIC_PORT:-3306}
      - MYSQL_DATABASE_NAME=tuco # 改动需要修改hoj-mysql镜像,默认为hoj
      - MYSQL_USERNAME=root
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-TuCO123456}
      - REDIS_HOST=${REDIS_HOST:-r-7xvxtli474xudpuuc8pd.redis.rds.aliyuncs.com}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-TuCO123456}
      - EMAIL_SERVER_HOST=${EMAIL_SERVER_HOST:-smtp.qq.com} # 请使用邮件服务的域名或ip
      - EMAIL_SERVER_PORT=${EMAIL_SERVER_PORT:-465} # 请使用邮件服务的端口号
      - EMAIL_USERNAME=${EMAIL_USERNAME:-your_email_username} # 请使用对应邮箱账号
      - EMAIL_PASSWORD=${EMAIL_PASSWORD:-your_email_password} # 请使用对应邮箱密码
      - OPEN_REMOTE_JUDGE=true # 是否开启各个remote judge
      # 开启虚拟判题请提供对应oj的账号密码 格式为
      # username1,username2,...
      # password1,password2,...
      - HDU_ACCOUNT_USERNAME_LIST=${HDU_ACCOUNT_USERNAME_LIST}
      - HDU_ACCOUNT_PASSWORD_LIST=${HDU_ACCOUNT_PASSWORD_LIST}
      - CF_ACCOUNT_USERNAME_LIST=${CF_ACCOUNT_USERNAME_LIST}
      - CF_ACCOUNT_PASSWORD_LIST=${CF_ACCOUNT_PASSWORD_LIST}
      - POJ_ACCOUNT_USERNAME_LIST=${POJ_ACCOUNT_USERNAME_LIST}
      - POJ_ACCOUNT_PASSWORD_LIST=${POJ_ACCOUNT_PASSWORD_LIST}
      - ATCODER_ACCOUNT_USERNAME_LIST=${ATCODER_ACCOUNT_USERNAME_LIST}
      - ATCODER_ACCOUNT_PASSWORD_LIST=${ATCODER_ACCOUNT_PASSWORD_LIST}
      - SPOJ_ACCOUNT_USERNAME_LIST=${SPOJ_ACCOUNT_USERNAME_LIST}
      - SPOJ_ACCOUNT_PASSWORD_LIST=${SPOJ_ACCOUNT_PASSWORD_LIST}
      - LIBRE_ACCOUNT_USERNAME_LIST=${LIBRE_ACCOUNT_USERNAME_LIST}
      - LIBRE_ACCOUNT_PASSWORD_LIST=${LIBRE_ACCOUNT_PASSWORD_LIST}
      # 是否强制使用配置文件的remote judge账号覆盖原有系统的账号列表
      - FORCED_UPDATE_REMOTE_JUDGE_ACCOUNT=${FORCED_UPDATE_REMOTE_JUDGE_ACCOUNT:-false}
    ports:
      - ${API_PORT:-8202}:${API_PORT:-8202}            # 暴露API服务端口
    # 滚动更新配置
    deploy:
      update_config:
        parallelism: 1       # 每次更新1个实例
        delay: 15s           # 更新间隔15秒
        failure_action: rollback  # 失败时回滚
        order: stop-first     # 先停止旧容器再启动新容器

  # 判题服务
  business-tuco-judge:
    # 使用阿里云镜像
    image: registry.cn-guangzhou.aliyuncs.com/glowxq/business:business-tuco-judge
    container_name: business-tuco-judge
    restart: always
    # 数据卷配置
    volumes:
      - ${TUCO_DATA_DIRECTORY}/testcase:/judge/test_case                # 测试用例目录
      - ${TUCO_DATA_DIRECTORY}/judge/log:/judge/log                    # 日志目录
      - ${TUCO_DATA_DIRECTORY}/judge/run:/judge/run                    # 运行目录
      - ${TUCO_DATA_DIRECTORY}/judge/spj:/judge/spj                    # 特殊判题程序目录
      - ${TUCO_DATA_DIRECTORY}/judge/interactive:/judge/interactive    # 交互程序目录
      - ${TUCO_DATA_DIRECTORY}/log/judgeserver:/judge/log/judgeserver  # 判题服务器日志
    # 环境变量配置
    environment:
      # 环境设置
      - ENV=${ENV:-prod}
      - TZ=Asia/Shanghai                                      # 时区设置
      - JAVA_OPTS=-Xms1024m -Xmx2048m                         # JVM 内存配置
      - JUDGE_SERVER_IP=${JUDGE_SERVER_IP}                 # 判题服务器IP
      - JUDGE_SERVER_PORT=${JUDGE_SERVER_PORT:-8203}          # 判题服务器端口
      - NACOS_URL=${NACOS_HOST:-**************}:${NACOS_PORT:-8200}  # Nacos 服务地址
      - NACOS_USERNAME=${NACOS_USERNAME:-nacos}               # Nacos 用户名
      - NACOS_PASSWORD=${NACOS_PASSWORD:-TuCO123456}          # Nacos 密码
      - MAX_TASK_NUM=${MAX_TASK_NUM:--1} # -1表示最大可接收判题任务数为cpu核心数+1
      - REMOTE_JUDGE_OPEN=${REMOTE_JUDGE_OPEN:-true} # 当前判题服务器是否开启远程虚拟判题功能
      - REMOTE_JUDGE_MAX_TASK_NUM=${REMOTE_JUDGE_MAX_TASK_NUM:--1} # -1表示最大可接收远程判题任务数为cpu核心数*2+1
      - PARALLEL_TASK=${PARALLEL_TASK:-default} # 默认沙盒并行判题程序数为cpu核心数
    # 端口映射
    ports:
      - ${JUDGE_SERVER_PORT:-8203}:${JUDGE_SERVER_PORT:-8203}  # 暴露判题服务端口
    # 健康检查
    healthcheck:
      test: curl -f http://127.0.0.1:${JUDGE_SERVER_PORT:-8203}/version || exit 1
      interval: 30s          # 每30秒检查一次
      timeout: 10s           # 超时时间10秒
      retries: 3             # 重试3次判定为失败
    privileged: true         # 开启特权模式
    shm_size: 512mb          # 共享内存大小
    # 滚动更新配置
    deploy:
      update_config:
        parallelism: 1       # 每次更新1个实例
        delay: 10s           # 更新间隔10秒
        order: stop-first   # 先启动新容器再停止旧容器

  # judge-tuc-判题服务
  #  business-tuco-judge-tuc:
  #    # 使用阿里云镜像
  #    image: registry.cn-guangzhou.aliyuncs.com/glowxq/business:business-tuco-judge-tuc
  #    container_name: business-tuco-judge-tuc
  #    #    restart: always
  #    # 数据卷配置
  #    volumes:
  #      - /mnt/data/my/tuco/judge_tuc/testcase:/judge/test_case                # 测试用例目录
  #      - /mnt/data/my/tuco/judge_tuc/judge/log:/judge/log                    # 日志目录
  #      - /mnt/data/my/tuco/judge_tuc/judge/run:/judge/run                    # 运行目录
  #      - /mnt/data/my/tuco/judge_tuc/judge/spj:/judge/spj                    # 特殊判题程序目录
  #      - /mnt/data/my/tuco/judge_tuc/judge/interactive:/judge/interactive    # 交互程序目录
  #      - /mnt/data/my/tuco/judge_tuc/log/judgeserver:/judge/log/judgeserver  # 判题服务器日志
  #    # 环境变量配置
  #    environment:
  #      - TZ=Asia/Shanghai                                      # 时区设置
  #      - PARALLEL_TASK=${PARALLEL_TASK:-default} # 默认沙盒并行判题程序数为cpu核心数
  #    # 端口映射
  #    ports:
  #      - "8201:5050"  # 暴露判题服务端口
  #    # 健康检查
  #    healthcheck:
  #      test: curl -f http://127.0.0.1:8201/version || exit 1
  #      interval: 30s          # 每 30秒检查一次
  #      timeout: 10s           # 超时时间 10秒
  #      retries: 3             # 重试 3 次判定为失败
  #    privileged: true         # 开启特权模式
  #    shm_size: 512mb          # 共享内存大小
  #    # 滚动更新配置
  #    deploy:
  #      update_config:
  #        parallelism: 1       # 每次更新1个实例
  #        delay: 10s           # 更新间隔10秒
  #        order: stop-first    # 先启动新容器再停止旧容器

  # 容器健康监控服务
  tuco-autohealth:
    image: willfarrell/autoheal
    container_name: tuco-autohealth
    restart: always
    environment:
      - AUTOHEAL_CONTAINER_LABEL=all  # 监控所有容器
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock  # Docker 套接字
