package top.hcode.hoj.gen;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/7
 */
// @Configuration
@Data
public class DataSource {

    @Value("${spring.datasource.url}")
    public String url = "*******************************************************************************************";

    @Value("${spring.datasource.username}")
    public String username = "root";

    @Value("${spring.datasource.password}")
    public String password = "TuCO123456";
}
