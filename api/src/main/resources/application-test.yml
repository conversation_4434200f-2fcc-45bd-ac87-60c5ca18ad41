# 默认以下配置默认会读取环境变量的值

# redis配置
redis-host: ${REDIS_HOST:r-7xvxtli474xudpuuc8pd.redis.rds.aliyuncs.com}
redis-port: ${REDIS_PORT:6379}
redis-database: ${REDIS_DATABASE:0}
redis-password: ${REDIS_PASSWORD:TuCO123456}

# 本服务连接mysql的地址和端口号
mysql-host: ${MYSQL_HOST:rm-7xvweu4nb7jz1xuin.mysql.rds.aliyuncs.com}
mysql-port: ${MYSQL_PORT:3306}
# mysql的公网地址和端口号
mysql-public-host: ${MYSQL_PUBLIC_HOST:rm-7xvweu4nb7jz1xuinno.mysql.rds.aliyuncs.com}
mysql-public-port: ${MYSQL_PUBLIC_PORT:3306}
mysql-username: ${MYSQL_USERNAME:root}
mysql-password: ${MYSQL_ROOT_PASSWORD:TuCO123456}
mysql-name: ${MYSQL_DATABASE_NAME:tuco}

# token密钥，default表示随机生成
jwt-token-secret: ${JWT_TOKEN_SECRET:default}
# token过期时间 单位s
jwt-token-expire: ${JWT_TOKEN_EXPIRE:86400}
# token可刷新的过期时间 单位s
jwt-token-fresh-expire: ${JWT_TOKEN_FRESH_EXPIRE:43200}

# 网站是否开启远程评测
open-remote-judge: ${OPEN_REMOTE_JUDGE:true}
# 调用评测服务的token, default表示随机生成
judge-token: ${JUDGE_TOKEN:default}

# 邮箱服务地址
email-host: ${EMAIL_SERVER_HOST:smtp.qq.com}
# 邮箱服务端口号
email-port: ${EMAIL_SERVER_PORT:465}
# 邮箱账号
email-username: ${EMAIL_USERNAME:your_email_username}
# 邮箱授权码
email-password: ${EMAIL_PASSWORD:your_email_password}

# 以下为各个remote judge平台的账号配置
hdu-username-list: ${HDU_ACCOUNT_USERNAME_LIST:}
hdu-password-list: ${HDU_ACCOUNT_PASSWORD_LIST:}
poj-username-list: ${POJ_ACCOUNT_USERNAME_LIST:}
poj-password-list: ${POJ_ACCOUNT_PASSWORD_LIST:}
cf-username-list: ${CF_ACCOUNT_USERNAME_LIST:}
cf-password-list: ${CF_ACCOUNT_PASSWORD_LIST:}
atcoder-username-list: ${ATCODER_ACCOUNT_USERNAME_LIST:}
atcoder-password-list: ${ATCODER_ACCOUNT_PASSWORD_LIST:}
spoj-username-list: ${SPOJ_ACCOUNT_USERNAME_LIST:}
spoj-password-list: ${SPOJ_ACCOUNT_PASSWORD_LIST:}
libreoj-username-list: ${LIBREOJ_ACCOUNT_USERNAME_LIST:}
libreoj-password-list: ${LIBREOJ_ACCOUNT_PASSWORD_LIST:}
# 是否强制用配置更新覆盖remote judge的账号
forced-update-remote-judge-account: ${FORCED_UPDATE_REMOTE_JUDGE_ACCOUNT:false}

spring:
  # 配置文件上传限制
  servlet:
    multipart:
      max-file-size: 512MB
      max-request-size: 512MB
  redis:
    host: ${redis-host}
    port: ${redis-port}
    timeout: 60000
    jedis:
      pool:
        min-idle: 10 #连接池中的最小空闲连接
        max-idle: 100 #连接池中的最大空闲连接
        max-active: 200 #连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 #连接池最大阻塞等待时间（使用负值表示没有限制）
    password: ${redis-password}
    database: ${redis-database}
  datasource:
    username: ${mysql-username}
    password: ${mysql-password}
    url: jdbc:mysql://${mysql-host}:${mysql-port}/${mysql-name}?useUnicode=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    initial-size: 10 # 初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
    min-idle: 20 # 最小连接池数量
    maxActive: 200 # 最大连接池数量
    maxWait: 60000 # 获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置
    timeBetweenEvictionRunsMillis: 60000 # 关闭空闲连接的检测时间间隔.Destroy线程会检测连接的间隔时间，如果连接空闲时间大于等于minEvictableIdleTimeMillis则关闭物理连接。
    minEvictableIdleTimeMillis: 300000 # 连接的最小生存时间.连接保持空闲而不被驱逐的最小时间
    validationQuery: SELECT 1 FROM DUAL # 验证数据库服务可用性的sql.用来检测连接是否有效的sql 因数据库方言而差, 例如 oracle 应该写成 SELECT 1 FROM DUAL
    testWhileIdle: true # 申请连接时检测空闲时间，根据空闲时间再检测连接是否有效.建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRun
    testOnBorrow: false # 申请连接时直接检测连接是否有效.申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
    testOnReturn: false # 归还连接时检测连接是否有效.归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
    poolPreparedStatements: true # 开启PSCache
    maxPoolPreparedStatementPerConnectionSize: 20 #设置PSCache值
    connectionErrorRetryAttempts: 3 # 连接出错后再尝试连接三次
    breakAfterAcquireFailure: true # 数据库服务宕机自动重连机制
    timeBetweenConnectErrorMillis: 300000 # 连接出错后重试时间间隔
    asyncInit: true # 异步初始化策略
    remove-abandoned: true # 是否自动回收超时连接
    remove-abandoned-timeout: 1800 # 超时时间(以秒数为单位)
    transaction-query-timeout: 6000 # 事务超时时间
    filters: stat,wall,log4j #数据库日志
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
  thymeleaf:
    encoding: UTF-8

shiro-redis:
  enabled: true
  redis-manager:
    host: ${redis-host}:${redis-port}
    password: ${redis-password}
    database: 0

logging:
  level:
    com:
      alibaba:
        nacos:
          client:
            naming: error
      gargoylesoftware: off
    root: info
  config: classpath:logback-spring.xml
  file:
    path: ./log/api