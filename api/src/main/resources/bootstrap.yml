project: Tuco
type: api
env: ${ENV:dev}
name: ${project}-${type}

# nacos配置
nacos-url: ${NACOS_URL:***********:8200}
#nacos-url: ${NACOS_URL:**************:8200}
nacos-username: ${NACOS_USERNAME:nacos}
nacos-password: ${NACOS_PASSWORD:TuCO123456}
nacos-switch-config: hoj-switch.yml
nacos-web-config: hoj-web.yml

# 消费者将要去访问的微服务名称（注册成功进入nacos的微服务提供者）
service-url:
  name: Tuco-judge # 服务名

app:
  meta:
    name: ${name}
    env: ${env}
    version: 1.0.0
    description: ${name}
    author: glowxq.com

server:
  port: ${BACKEND_SERVER_PORT:8202}
  ssl:
    enabled: false
    key-store-type: PKCS12
    # src/main/resources
    key-store: "classpath:ssl/oj.aicx.cc.pfx"
    # 从 pfx-password.txt 中获取
    key-store-password: "iw6i663w"
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
    encoding:
      force: true

alibaba:
  accessKey: "LTAI5tHocU3e9FNYftGDzCbX"
  secretKey: "******************************"
  sms:
    accessKey: ${alibaba.accessKey}
    secretKey: ${alibaba.secretKey}
  # OSS 配置
  oss:
    tenantId: "000000"
    endpoint: "oss-cn-guangzhou.aliyuncs.com"
    fileService: "aliyun"
    domain: ""
    accessKey: ${alibaba.accessKey}
    secretKey: ${alibaba.secretKey}
    #  留空默认根据AppType创建
    bucketName: "tuco"
    region: "cn-guangzhou"
    # true表示使用HTTPS，false表示不使用
    enabled-https: true
    http-prefix: "https://"
    # 文件大小 kb: 1M
    max-file-size: 1048576000
    timeout: 5000
    access-policy-type: "public"


spring:
  profiles:
    active: ${env}
  application:
    name: ${name}
  cloud:
    nacos:
      discovery:
        username: ${nacos-username}
        password: ${nacos-password}
        namespace: ${env}
        group: ${project}  # 指定分组
        server-addr: ${nacos-url} # Nacos 作为服务注册中心 nacos的地址
      config:
        username: ${nacos-username}
        password: ${nacos-password}
        namespace: ${env}
        server-addr: ${nacos-url}  #Nacos 作为配置中心地址 nacos的地址
        file-extension: yml #指定yaml格式的配置
        group: ${project}  # 指定分组
        type: yaml
        prefix: hoj
      url: http://${nacos-url}

# ${spring.application.name}-${spring.profile.active}.${spring.cloud.naces.config.file-extension}
# ${spring.cloud.nacos.config.prefix}-${spring.profile.active}.${spring.cloud.naces.config.file-extension}
# hoj-prod.yml

