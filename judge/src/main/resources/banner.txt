${AnsiColor.BRIGHT_YELLOW}

        ,--,               ,----..
      ,--.'|  ,----..     /   /   \      ,---,        ,---,.
   ,--,  | : /   /   \   /   .     :   .'  .' `\    ,'  .' |
,---.'|  : '|   :     : .   /   ;.  \,---.'     \ ,---.'   |
|   | : _' |.   |  ;. /.   ;   /  ` ;|   |  .`\  ||   |   .'
:   : |.'  |.   ; /--` ;   |  ; \ ; |:   : |  '  |:   :  |-,
|   ' '  ; :;   | ;    |   :  | ; | '|   ' '  ;  ::   |  ;/|
'   |  .'. ||   : |    .   |  ' ' ' :'   | ;  .  ||   :   .'
|   | :  | '.   | '___ '   ;  \; /  ||   | :  |  '|   |  |-,
'   : |  : ;'   ; : .'| \   \  ',  / '   : | /  ; '   :  ;/|
|   | '  ,/ '   | '/  :  ;   :    /  |   | '` ,/  |   |    \
;   : ;--'  |   :    /    \   \ .'   ;   :  .'    |   :   .'
|   ,/       \   \ .'      `---`     |   ,.'      |   | ,'
'---'         `---`                  '---'        `----'
            Hcode Online Judge(HOJ) - JudgeServer
                    <AUTHOR>
                    @Last Update 20241013
          ->Github<- https://www.github.com/HimitZH/HOJ
          ->Gitee<-  https://gitee.com/himitzh0730/hoj