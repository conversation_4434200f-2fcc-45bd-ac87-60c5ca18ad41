mybatis-plus:
  mapper-locations: classpath*:top/hcode/hoj/mapper/xml/**Mapper.xml
  type-aliases-package: top.hcode.hoj.pojo.entity
  # 关闭打印 mybatis-plus 的 LOGO
  global-config:
    banner: false

feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: basic # 日志级别

logging:
  level:
    com:
      alibaba:
        nacos: error
      gargoylesoftware: off
    root: info
  config: classpath:logback-spring.xml
  file:
    path: ./log/judge


# 暴露监控
management:
  endpoints:
    web:
      exposure:
        include: info,health
