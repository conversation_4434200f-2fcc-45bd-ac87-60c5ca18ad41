project: Tuco
type: judge
env: ${ENV:dev}
name: ${project}-${type}

# nacos配置
#nacos-url: ${NACOS_URL:***********:8200}
nacos-url: ${NACOS_URL:**************:8200}
nacos-username: ${NACOS_USERNAME:nacos}
nacos-password: ${NACOS_PASSWORD:TuCO123456}
nacos-switch-config: hoj-switch.yml
nacos-web-config: hoj-web.yml


hoj-judge-server:
  max-task-num: ${MAX_TASK_NUM:-1} # -1表示最大并行任务数为cpu核心数+1
  ip: ${JUDGE_SERVER_IP:-1} # -1表示使用默认本地ipv4，若是部署其它服务器，务必使用公网ip
  port: ${JUDGE_SERVER_PORT:8203}  # 端口号
  name: ${name} # 判题机名字 唯一不可重复！！！
  remote-judge:
    open: ${REMOTE_JUDGE_OPEN:true} # 当前判题服务器是否开启远程虚拟判题功能
    max-task-num: ${REMOTE_JUDGE_MAX_TASK_NUM:-1}  # -1表示最大并行任务数为cpu核心数*2+1

server:
  port: ${hoj-judge-server.port}
  ssl:
    enabled: false
    key-store-type: PKCS12
    # src/main/resources
    key-store: "classpath:ssl/oj.aicx.cc.pfx"
    # 从 pfx-password.txt 中获取
    key-store-password: "iw6i663w"
  servlet:
    encoding:
      force: true

spring:
  profiles:
    active: ${env}
  application:
    name: ${name}
  cloud:
    nacos:
      discovery:
        username: ${nacos-username}
        password: ${nacos-password}
        namespace: ${env}
        group: ${project}  # 指定分组
        server-addr: ${nacos-url} # Nacos 作为服务注册中心 nacos的地址
      config:
        username: ${nacos-username}
        password: ${nacos-password}
        namespace: ${env}
        group: ${project}  # 指定分组
        server-addr: ${nacos-url}  #Nacos 作为配置中心地址 nacos的地址
        file-extension: yml #指定yaml格式的配置
        prefix: hoj
      url: http://${nacos-url}

# ${spring.application.name}-${spring.profile.active}.${spring.cloud.naces.config.file-extension}
# ${spring.cloud.nacos.config.prefix}-${spring.profile.active}.${spring.cloud.naces.config.file-extension}
# hoj-prod.yml
