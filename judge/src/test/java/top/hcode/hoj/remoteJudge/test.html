
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN">
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta name="X-Csrf-Token" content="9712b77f90ae1bf64920f4bd65248fab"/>
    <meta id="viewport" name="viewport" content="width=device-width, initial-scale=0.01"/>


    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery-1.8.3.js"></script>
    <script type="application/javascript">
        window.standaloneContest = false;
        function adjustViewport() {
            var screenWidthPx = Math.min($(window).width(), window.screen.width);
            var siteWidthPx = 1100; // min width of site
            var ratio = Math.min(screenWidthPx / siteWidthPx, 1.0);
            var viewport = "width=device-width, initial-scale=" + ratio;
            $('#viewport').attr('content', viewport);
            var style = $('<style>html * { max-height: 1000000px; }</style>');
            $('html > head').append(style);
        }

        if ( /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ) {
            adjustViewport();
        }

        /* Protection against trailing dot in domain. */
        let hostLength = window.location.host.length;
        if (hostLength > 1 && window.location.host[hostLength - 1] === '.') {
            window.location = window.location.protocol + "//" + window.location.host.substring(0, hostLength - 1);
        }
    </script>
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="expires" content="-1">
    <meta http-equiv="profileName" content="f1">
    <meta name="google-site-verification" content="OTd2dN5x4nS4OPknPI9JFg36fKxjqY0i1PSfFPv_J90"/>
    <meta property="fb:admins" content="100001352546622" />
    <meta property="og:image" content="//sta.codeforces.com/s/76577/images/codeforces-telegram-square.png" />
    <link rel="image_src" href="//sta.codeforces.com/s/76577/images/codeforces-telegram-square.png" />
    <meta property="og:title" content="Codeforces"/>
    <meta property="og:description" content=""/>

    <meta property="og:site_name" content="Codeforces"/>
    <meta name="uc" content="6901fe70f6e71be1ad9eb65904675516f1b7b3de"/>
    <meta name="usmc" content="eea42e7ba28d03216e0b8409e372395c9a52975b"/>




    <meta name="utc_offset" content="+03:00"/>
    <meta name="verify-reformal" content="f56f99fd7e087fb6ccb48ef2" />
    <title>Codeforces</title>
    <meta name="description" content="Codeforces. Programming competitions and contests, programming community" />
    <meta name="keywords" content="programming algorithm contest competition informatics olympiads c++ java graphs vkcup" />
    <meta name="robots" content="index, follow" />

    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/font-awesome.min.css" type="text/css" charset="utf-8" />

    <link href='//fonts.googleapis.com/css?family=PT+Sans+Narrow:400,700&subset=latin,cyrillic' rel='stylesheet' type='text/css'>
    <link href='//fonts.googleapis.com/css?family=Cuprum&subset=latin,cyrillic' rel='stylesheet' type='text/css'>


    <link rel="apple-touch-icon" sizes="57x57" href="//sta.codeforces.com/s/76577/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="//sta.codeforces.com/s/76577/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="//sta.codeforces.com/s/76577/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="//sta.codeforces.com/s/76577/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="//sta.codeforces.com/s/76577/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="//sta.codeforces.com/s/76577/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="//sta.codeforces.com/s/76577/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="//sta.codeforces.com/s/76577/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="//sta.codeforces.com/s/76577/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"  href="//sta.codeforces.com/s/76577/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="//sta.codeforces.com/s/76577/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="//sta.codeforces.com/s/76577/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="//sta.codeforces.com/s/76577/favicon-16x16.png">
    <link rel="manifest" href="//sta.codeforces.com/s/76577/manifest.json">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="//sta.codeforces.com/s/76577/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">

    <!--CombineResourcesFilter-->
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/prettify.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/clear.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/style.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/ttypography.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/problem-statement.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/second-level-menu.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/roundbox.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/datatable.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/table-form.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/topic.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/jquery.jgrowl.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/facebox.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/jquery.wysiwyg.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/jquery.autocomplete.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/codeforces.datepick.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/colorbox.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/jquery.drafts.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/css/community.css" type="text/css" charset="utf-8" />

    <!-- MathJax -->
    <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
      tex2jax: {inlineMath: [['$$$','$$$']], displayMath: [['$$$$$$','$$$$$$']]}
    });
    MathJax.Hub.Register.StartupHook("End", function () {
        Codeforces.runMathJaxListeners();
    });
    </script>
    <script type="text/javascript" async
            src="https://assets.codeforces.com/mathjax/MathJax.js?config=TeX-AMS_HTML-full"
    >
    </script>
    <!-- /MathJax -->

    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/prettify/prettify.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/moment-with-locales.min.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/pushstream.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.easing.min.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.lavalamp.min.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.jgrowl.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.swipe.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.hotkeys.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/facebox.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.wysiwyg.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/controls/wysiwyg.colorpicker.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/controls/wysiwyg.table.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/controls/wysiwyg.image.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/controls/wysiwyg.link.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.autocomplete.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/ua-parser.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.datepick.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.ie6blocker.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.colorbox-min.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.ba-bbq.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/jquery.drafts.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/clipboard.min.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/autosize.min.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/sjcl.js"></script>
    <script type="text/javascript" src="/scripts/be3efcb04dee064cbf1f0513d793ff4c/en/codeforces-options.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/codeforces.js?v=20160131"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/EventCatcher.js?v=20160131"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/js/preparedVerdictFormats-en.js"></script>
    <!--/CombineResourcesFilter-->

    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/markitup/skins/markitup/style.css" type="text/css" charset="utf-8" />
    <link rel="stylesheet" href="//sta.codeforces.com/s/76577/markitup/sets/markdown/style.css" type="text/css" charset="utf-8" />


    <script type="text/javascript" src="//sta.codeforces.com/s/76577/markitup/jquery.markitup.js"></script>
    <script type="text/javascript" src="//sta.codeforces.com/s/76577/markitup/sets/markdown/set.js"></script>

    <!--[if IE]>
    <style>
        #sidebar {
            padding-left: 1em;
            margin: 1em 1em 1em 0;
        }
    </style>
    <![endif]-->



</head>
<body class=" "><span style='display:none;' class='csrf-token' data-csrf='9712b77f90ae1bf64920f4bd65248fab'>&nbsp;</span>

<!-- .notificationTextCleaner used in Codeforces.showAnnouncements() -->
<div class="notificationTextCleaner" style="font-size: 0"></div>
<div class="button-up" style="display: none; opacity: 0.7; width: 50px; height:100%; position: fixed; left: 0; top: 0; cursor: pointer; text-align: center; line-height: 35px; color: #d3dbe4; font-weight: bold; font-size: 3.0rem;"><i class="icon-circle-arrow-up"></i></div>
<div class="verdictPrototypeDiv" style="display: none;"></div>

<!-- Codeforces JavaScripts. -->
<script type="text/javascript">
    String.prototype.hashCode = function() {
        var hash = 0, i, chr;
        if (this.length === 0) return hash;
        for (i = 0; i < this.length; i++) {
            chr   = this.charCodeAt(i);
            hash  = ((hash << 5) - hash) + chr;
            hash |= 0; // Convert to 32bit integer
        }
        return hash;
    };

    var queryMobile = Codeforces.queryString.mobile;
    if (queryMobile === "true" || queryMobile === "false") {
        Codeforces.putToStorage("useMobile", queryMobile === "true");
    } else {
        var useMobile = Codeforces.getFromStorage("useMobile");
        if (useMobile === true || useMobile === false) {
            if (useMobile != false) {
                Codeforces.redirect(Codeforces.updateUrlParameter(document.location.href, "mobile", useMobile));
            }
        }
    }
</script>

<script type="text/javascript">
    if (window.parent.frames.length > 0) {
        window.stop();
    }
</script>



<script type="text/javascript">
    window.fbAsyncInit = function() {
        FB.init({
            appId      : '554666954583323',
            xfbml      : true,
            version    : 'v2.8'
        });
        FB.AppEvents.logPageView();
    };

    (function(d, s, id){
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) {return;}
        js = d.createElement(s); js.id = id;
        js.src = "//connect.facebook.net/en_US/sdk.js";
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
</script>


<script type="text/javascript">
    $(document).ready(function () {
        (function () {
            jQuery.expr[':'].containsCI = function(elem, index, match) {
                return !match || !match.length || match.length < 4 || !match[3] || (
                    elem.textContent || elem.innerText || jQuery(elem).text() || ''
                ).toLowerCase().indexOf(match[3].toLowerCase()) >= 0;
            }
        }(jQuery));

        $.ajaxPrefilter(function(options, originalOptions, xhr) {
            var csrf = Codeforces.getCsrfToken();

            if (csrf) {
                var data = originalOptions.data;
                if (originalOptions.data !== undefined) {
                    if (Object.prototype.toString.call(originalOptions.data) === '[object String]') {
                        data = $.deparam(originalOptions.data);
                    }
                } else {
                    data = {};
                }
                options.data = $.param($.extend(data, { csrf_token: csrf }));
            }
        });

        window.getCodeforcesServerTime = function(callback) {
            $.post("/data/time", {}, callback, "json");
        }

        window.updateTypography = function () {
            $("div.ttypography code").addClass("tt");
            $("div.ttypography pre>code").addClass("prettyprint").removeClass("tt");
            $("div.ttypography table").addClass("bordertable");
            prettyPrint();
        }

        $.ajaxSetup({ scriptCharset: "utf-8" ,contentType: "application/x-www-form-urlencoded; charset=UTF-8", headers: {
                'X-Csrf-Token': Codeforces.getCsrfToken()
            }});

        window.updateTypography();

        Codeforces.signForms();

        setTimeout(function() {
            $(".second-level-menu-list").lavaLamp({
                fx: "backout",
                speed: 700
            });
        }, 100);


        Codeforces.countdown();
        $("a[rel='photobox']").colorbox();

        var count = 0;

        function getDelay() {
            var delay = 0;
            var last = Codeforces.getFromStorage("LastOnlineTimeUpdaterMillis", 0);
            if (last && last != null) {
                var period = count < 20 ? 180 * 1000 : 360 * 1000;
                delay = period - (new Date().getTime() - last);
                if (delay < 0)
                    delay = 0;
            }
            return delay;
        }

        window.setInterval(function () {
            if (getDelay() <= 0 && count < 120) {
                ++count;
                Codeforces.ping("/data/update-online");
            }
        }, 5000);

        var handle = "2018030402055";
        $("a.rated-user:contains(" + handle + "), span.participant:contains(" + handle + ")").each(function () {
            if ($(this).text() == handle) {
                var td = $(this).parent();
                var tr = $(this).parent().parent();
                if (td.is("td") && tr.is("tr")) {
                    tr.addClass("highlighted-row");
                }
            }
        });

        function showAnnouncements(json) {
            //info("j=" + JSON.stringify(json));

            if (json.t != "a") {
                return;
            }

            setTimeout(function() {
                Codeforces.showAnnouncements(json.d, "en");
            }, Math.random() * 500);
        }

        function showEventCatcherUserMessage(json) {
            if (json.t == "s") {
                var points = json.d[5];
                var passedTestCount = json.d[7];
                var judgedTestCount = json.d[8];
                var verdict = preparedVerdictFormats[json.d[12]];
                var verdictPrototypeDiv = $(".verdictPrototypeDiv");
                verdictPrototypeDiv.html(verdict);
                if (judgedTestCount != null && judgedTestCount != undefined) {
                    verdictPrototypeDiv.find(".verdict-format-judged").text(judgedTestCount);
                }
                if (passedTestCount != null && passedTestCount != undefined) {
                    verdictPrototypeDiv.find(".verdict-format-passed").text(passedTestCount);
                }
                if (points != null && points != undefined) {
                    verdictPrototypeDiv.find(".verdict-format-points").text(points);
                }
                Codeforces.showMessage(verdictPrototypeDiv.text());
            }
        }

        $(".clickable-title").each(function() {
            var title = $(this).attr("data-title");
            if (title) {
                var tmp = document.createElement("DIV");
                tmp.innerHTML = title;
                $(this).attr("title", tmp.textContent || tmp.innerText || "");
            }
        });

        $(".clickable-title").click(function() {
            var title = $(this).attr("data-title");
            if (title) {
                Codeforces.alert(title);
            } else {
                Codeforces.alert($(this).attr("title"));
            }
        }).css("position", "relative").css("bottom", "3px");

        Codeforces.showDelayedMessage();

        Codeforces.reformatTimes();

        //Codeforces.initializePubSub();
        if (window.codeforcesOptions.subscribeServerUrl) {
            window.eventCatcher = new EventCatcher(
                window.codeforcesOptions.subscribeServerUrl,
                [
                    Codeforces.getGlobalChannel(),
                    Codeforces.getUserChannel(),
                    Codeforces.getUserShowMessageChannel(),
                    Codeforces.getContestChannel(),
                    Codeforces.getParticipantChannel(),
                    Codeforces.getTalkChannel()
                ]
            );

            if (Codeforces.getParticipantChannel()) {
                window.eventCatcher.subscribe(Codeforces.getParticipantChannel(), function(json) {
                    showAnnouncements(json);
                });
            }

            if (Codeforces.getContestChannel()) {
                window.eventCatcher.subscribe(Codeforces.getContestChannel(), function(json) {
                    showAnnouncements(json);
                });
            }

            if (Codeforces.getGlobalChannel()) {
                window.eventCatcher.subscribe(Codeforces.getGlobalChannel(), function(json) {
                    showAnnouncements(json);
                });
            }

            if (Codeforces.getUserChannel()) {
                window.eventCatcher.subscribe(Codeforces.getUserChannel(), function(json) {
                    showAnnouncements(json);
                });
            }

            if (Codeforces.getUserShowMessageChannel()) {
                window.eventCatcher.subscribe(Codeforces.getUserShowMessageChannel(), function(json) {
                    showEventCatcherUserMessage(json);
                });
            }
        }

        Codeforces.setupContestTimes("/data/contests");
        Codeforces.setupSpoilers();
        Codeforces.setupTutorials("/data/problemTutorial");
    });
</script>

<script type="text/javascript">
    var _gaq = _gaq || [];
    _gaq.push(['_setAccount', 'UA-743380-5']);
    _gaq.push(['_trackPageview']);

    (function () {
        var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
        ga.src = (document.location.protocol == 'https:' ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
    })();
</script>


<div id="body">
    <script src="//sta.codeforces.com/s/76577/js/swfobject-2.2.min.js" type="text/javascript"></script>
    <script src="//sta.codeforces.com/s/76577/js/ftaa.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(function() {
            if (window.evercookie) {
                var ec = new evercookie({
                    history: false,
                    silverlight: false,
                    baseurl: '',
                    asseturi: '/assets',
                    phpuri: '/2fdcd78',
                    pngPath: '/eps',
                    etagPath: '/ees',
                    cachePath: '/ecs'
                });

                var randomNumber = function () {
                    return Math.random().toString(36).substr(2);
                };

                var randomToken = function () {
                    return (randomNumber() + randomNumber()).substring(0, 18);
                };

                window._ftaa = "";

                ec.get("70a7c28f3de", function (value) {
                    window._ftaa = value;
                    if (!window._ftaa) {
                        window._ftaa = randomToken();
                        ec.set("70a7c28f3de", window._ftaa);
                    }
                    $.post("/data/empty", {bfaa: window._bfaa, ftaa: window._ftaa});
                });
            } else {
                window._ftaa = "n/a";
                $.post("/data/empty", {bfaa: window._bfaa, ftaa: window._ftaa});
            }
        });
    </script>

    <script src="//sta.codeforces.com/s/76577/js/bfaa.js" type="text/javascript"></script>
    <script type="text/javascript">
        $(function() {
            var fpCallback = function() {
                Fingerprint2.get({}, function(components) {
                    window._bfaa = Fingerprint2.x64hash128(components.map(function (pair) { return pair.value }).join(), 31);
                    $.post("/data/empty", {bfaa: window._bfaa, ftaa: window._ftaa});
                });
            };
            if (typeof Fingerprint2 !== 'undefined') {
                if (window.requestIdleCallback) {
                    requestIdleCallback(fpCallback)
                } else {
                    setTimeout(fpCallback, 500)
                }
            } else {
                window._bfaa = "n/a";
                $.post("/data/empty", {bfaa: window._bfaa, ftaa: window._ftaa});
            }
        });
    </script>
    <script type="application/javascript">
        window.setTimeout(function () {
            var url = location.href;
            var hashCode32 = Math.abs(url.hashCode()).toString();
            var hashCode = "p" + hashCode32 + Math.abs((hashCode32 + url).hashCode());
            var pvs = Codeforces.getFromStorage("pvs" + hashCode);
            if (!pvs) {
                $.post("/data/apv", {
                    ftaa: window._ftaa,
                    bfaa: window._bfaa,
                    url: url
                }, function (json) {
                    if (json["success"] === "true") {
                        Codeforces.putToStorage("pvs" + hashCode, true);
                    }
                }, "json");
            }
        }, 5000);
    </script>


    <div class="side-bell" style="visibility: hidden; display: none; opacity: 0.7; width: 40px; position: fixed; right: 0; top: 0; cursor: pointer; text-align: center; line-height: 35px; color: #d3dbe4; font-weight: bold; font-size: 1.5rem;">
    <span class="icon-stack" style="width: 100%;">
        <i class="icon-circle icon-stack-base"></i>
        <i class="icon-bell-alt icon-light"></i>
    </span>
        <br/>
        <span class="side-bell__count" style="position: relative; top: -10px;"></span>
    </div>


    <div class="header-bell" style="display: none;">
        <div class="header-bell__img"><span class="header-bell__count"></span></div>

        <div class="bell-details">
            <div class="bell-details__proto-row-comment">
                <a class="bell-details__thumbnail-a" href="#"><img src=""/></a>
                <div class="bell-details__comment"></div>
                <div class="bell-details__time"></div>
            </div>
            <div class="bell-details__proto-row-blog-entry">
                <a class="bell-details__thumbnail-a" href="#"><img src=""/></a>
                <div class="bell-details__blog-entry"></div>
                <div class="bell-details__time"></div>
            </div>
            <div class="bell-details__proto-row-comment-reply">
                <a class="bell-details__thumbnail-a" href="#"><img src=""/></a>
                <div class="bell-details__comment-reply"></div>
            </div>

            <div class="bell-details__no-new">
                There are no new notifications
            </div>

            <div class="bell-details__recent-link">
                <a href="/notifications">History (at most 100 notifications)</a>
            </div>
        </div>
    </div>

    <style>
        .header-bell {
            display: inline-block;
            position: relative;
            cursor: pointer;
        }

        .header-bell__img {
            opacity: 0.5;
            background-image: url("//sta.codeforces.com/s/76577/images/icons/bell_inactive-20x20.png");
            position: relative;
            bottom: 1px;
            width: 20px;
            height: 20px;
        }

        .header-bell__img-active  {
            opacity: 1 !important;
            background-image: url("//sta.codeforces.com/s/76577/images/icons/bell_active-20x20.png") !important;
        }

        .header-bell__count {
            position: absolute;
            top: 8px;
            left: 18px;
            font-weight: bold;
            font-size: 10px;
        }

        .bell-details {
            display: none;
            z-index: 5;
            border: 1px solid rgb(185, 185, 185);
            width: 40rem;
            max-height: 60rem;
            padding: 0;
            position: absolute;
            top: 20px;
            right: 0;
            background-color: white;
            overflow: scroll;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .bell-details.sidebar-bell {
            position: fixed;
            right: 50px;
        }

        .bell-details__no-new {
            font-size: 1.3rem;
            display: block;
            text-align: center;
            color: #888 !important;
            padding: 1em;
        }

        .bell-details__recent-link {
            margin: 0;
            padding: 0.5em;
            text-align: center;
            border-top: 1px solid rgb(185, 185, 185);
        }

        .bell-details__recent-link a {
            font-size: 1.2rem;
        }

        .bell-details__proto-row-comment {
            display: none;
        }

        .bell-details__proto-row-blog-entry {
            display: none;
        }

        .bell-details__proto-row-comment-reply {
            display: none;
        }

        .bell-details__row {
            text-align: left;
            margin: 0.5em;
            padding: 0.25em 0.25em 0.75em 0.25em;
            clear: both;
        }

        .bell-details__row:hover {
            cursor: pointer;
            background-color: #def;
        }

        .bell-details__row-active {
            background-color: #def;
        }

        .bell-details__thumbnail-a {
            text-decoration: none;
        }

        .bell-details__row img {
            zoom: 50%;
            margin-right: 2em;
            float: left;
        }

        .bell-details__comment {
            padding-left: 45px;
            font-size: 1.3rem;
        }

        .bell-details__blog-entry {
            padding-left: 45px;
            font-size: 1.3rem;
        }

        .bell-details__comment-reply {
            padding-left: 45px;
            font-size: 1.3rem;
        }

        .bell-details__time {
            padding-left: 45px;
            margin-top: 0.2em;
            font-size: 1.1rem;
            color: #888 !important;
        }
    </style>

    <script>
        $(function () {
            if ($(window).width() < 1600) {
                $('.side-bell').css('width', '30px').css('line-height', '30px').css('font-size', '10px');
            }

            if ($(window).width() >= 1200) {
                $ (window).scroll (function () {
                    if ($ (this).scrollTop () > 100) {
                        $(".bell-details").addClass("sidebar-bell");
                        $ ('.side-bell').fadeIn();
                    } else {
                        $(".bell-details").removeClass("sidebar-bell");
                        $ ('.side-bell').fadeOut();
                    }
                });

                $('.side-bell').hover(function () {
                    $(this).animate({
                        'opacity':'1'
                    }).css({'color':'#6a86a4'});
                }, function () {
                    $(this).animate({
                        'opacity':'0.7'
                    }).css({'color':'#d3dbe4'});
                });
            }

            $(".lang-chooser>div:first-child").prepend($("<span style='position: relative; bottom: 8px; padding: 0 0.5em;'>|</span>")).prepend($(".header-bell").show());
            function Bell() {
                var bell = this;
                var notifications = JSON.parse("[]");

                bell.notifications = {};

                for (var i in notifications) {
                    if (notifications.hasOwnProperty(i)) {
                        bell.notifications[notifications[i].id] = notifications[i];
                    }
                }

                function onClick(notification) {
                    var notificationIds = [];
                    var reason = notification.data.reasonType + "#" + notification.reasonId;
                    for (id in bell.notifications) {
                        if (bell.notifications.hasOwnProperty(id)) {
                            var otherNotification = bell.notifications[id];
                            var otherReason = otherNotification.data.reasonType + "#" + otherNotification.reasonId;
                            if (otherReason == reason) {
                                notificationIds.push(otherNotification.id);
                            }
                        }
                    }
                    $.post("/data/notification", {action: "markAsRead", notificationIds: notificationIds.join(",")}, function(result) {
                        Codeforces.redirect(notification.data.url);
                    }, "json");
                }

                var $bellDetails = $(".bell-details");
                bell.newCommentRow = function (notification) {
                    var row = $(".bell-details__proto-row-comment").clone();
                    row.addClass("bell-details__row").attr("data-notificationId", notification.id);
                    row.find(".bell-details__thumbnail-a").attr("href", notification.data.reasonUserUrl);
                    row.find("img").attr("src", notification.data.reasonUserThumbnailUrl);
                    row.find(".bell-details__comment").html(
                        "ReasonUser mentioned you in a comment".replace("ReasonUser", notification.data.reasonUserHtml)
                    );
                    row.find(".bell-details__time").html(
                        "<span class='format-systemtimewithseconds'>"
                        + notification.creationTimeString
                        + "</span>"
                    );
                    row.removeClass("bell-details__proto-row-comment");
                    row.click(function() {
                        onClick(notification);
                        return false;
                    });
                    return row;
                };
                bell.newFriendCommentRow = function (notification) {
                    var row = $(".bell-details__proto-row-comment").clone();
                    row.addClass("bell-details__row").attr("data-notificationId", notification.id);
                    row.find(".bell-details__thumbnail-a").attr("href", notification.data.reasonUserUrl);
                    row.find("img").attr("src", notification.data.reasonUserThumbnailUrl);
                    row.find(".bell-details__comment").html(
                        "ReasonUser wrote a comment".replace("ReasonUser", notification.data.reasonUserHtml)
                    );
                    row.find(".bell-details__time").html(
                        "<span class='format-systemtimewithseconds'>"
                        + notification.creationTimeString
                        + "</span>"
                    );
                    row.removeClass("bell-details__proto-row-comment");
                    row.click(function() {
                        onClick(notification);
                        return false;
                    });
                    return row;
                };
                bell.newCommentReplyRow = function (notification) {
                    var row = $(".bell-details__proto-row-comment-reply").clone();
                    row.addClass("bell-details__row").attr("data-notificationId", notification.id);
                    row.find(".bell-details__thumbnail-a").attr("href", notification.data.reasonUserUrl);
                    row.find("img").attr("src", notification.data.reasonUserThumbnailUrl);
                    row.find(".bell-details__comment-reply").html(
                        "ReasonUser replied to your comment".replace("ReasonUser", notification.data.reasonUserHtml)
                    );
                    row.find(".bell-details__time").html(
                        "<span class='format-systemtimewithseconds'>"
                        + notification.creationTimeString
                        + "</span>"
                    );
                    row.removeClass("bell-details__proto-row-comment-reply");
                    row.click(function() {
                        onClick(notification);
                        return false;
                    });
                    return row;
                };
                bell.newBlogEntryRow = function (notification) {
                    var row = $(".bell-details__proto-row-blog-entry").clone();
                    row.addClass("bell-details__row").attr("data-notificationId", notification.id);
                    row.find(".bell-details__thumbnail-a").attr("href", notification.data.reasonUserUrl);
                    row.find("img").attr("src", notification.data.reasonUserThumbnailUrl);
                    row.find(".bell-details__blog-entry").html(
                        "ReasonUser mentioned you in a blog entry".replace("ReasonUser", notification.data.reasonUserHtml)
                    );
                    row.find(".bell-details__time").html(
                        "<span class='format-systemtimewithseconds'>"
                        + notification.creationTimeString
                        + "</span>"
                    );
                    row.removeClass("bell-details__proto-row-blog-entry");
                    row.click(function() {
                        onClick(notification);
                        return false;
                    });
                    return row;
                };
                bell.newFriendBlogEntryRow = function (notification) {
                    var row = $(".bell-details__proto-row-blog-entry").clone();
                    row.addClass("bell-details__row").attr("data-notificationId", notification.id);
                    row.find(".bell-details__thumbnail-a").attr("href", notification.data.reasonUserUrl);
                    row.find("img").attr("src", notification.data.reasonUserThumbnailUrl);
                    row.find(".bell-details__blog-entry").html(
                        "ReasonUser wrote a blog entry".replace("ReasonUser", notification.data.reasonUserHtml)
                    );
                    row.find(".bell-details__time").html(
                        "<span class='format-systemtimewithseconds'>"
                        + notification.creationTimeString
                        + "</span>"
                    );
                    row.removeClass("bell-details__proto-row-blog-entry");
                    row.click(function() {
                        onClick(notification);
                        return false;
                    });
                    return row;
                };
                bell.newProposalCommentRow = function (notification) {
                    var row = $(".bell-details__proto-row-comment-reply").clone();
                    row.addClass("bell-details__row").attr("data-notificationId", notification.id);
                    row.find(".bell-details__thumbnail-a").attr("href", notification.data.reasonUserUrl);
                    row.find("img").attr("src", notification.data.reasonUserThumbnailUrl);
                    row.find(".bell-details__comment-reply").html(
                        "ReasonUser commented on ProposalType Proposal".replace("ReasonUser", notification.data.reasonUserHtml)
                            .replace("ProposalType", "Contest" == notification.data.proposalType ? "contest proposal" : "problem proposal")
                            .replace("Proposal", $("<div/>").text(notification.data.proposalName).html())
                    );
                    row.find(".bell-details__time").html(
                        "<span class='format-systemtimewithseconds'>"
                        + notification.creationTimeString
                        + "</span>"
                    );
                    row.removeClass("bell-details__proto-row-comment-reply");
                    row.click(function() {
                        onClick(notification);
                        return false;
                    });
                    return row;
                };

                bell.renderRow = function (notification) {
                    var $row = $(".bell-details__row[data-notificationId='" + notification.id + "']");

                    if ($bellDetails.css("display") != "block" && notification.read) {
                        $row.hide();
                        return;
                    }

                    notification.data = JSON.parse(notification.dataJson);

                    var reason = notification.data.reasonType + "#" + notification.reasonId;

                    var has = false;
                    $row.each(function () {
                        has = true;
                    });
                    if ($(".bell-details__row[data-reason='" + reason + "']").length) {
                        has = true;
                    }

                    if (!has) {
                        $(".side-bell").css("visibility", "visible");
                        var newRow;
                        if (notification.type == "Comment") {
                            newRow = bell.newCommentRow(notification);
                        } else if (notification.type == "BlogEntry") {
                            newRow = bell.newBlogEntryRow(notification);
                        } else if (notification.type == "CommentReply") {
                            newRow = bell.newCommentReplyRow(notification);
                        } else if (notification.type == "FriendComment") {
                            newRow = bell.newFriendCommentRow(notification);
                        } else if (notification.type == "FriendBlogEntry") {
                            newRow = bell.newFriendBlogEntryRow(notification);
                        } else if (notification.type == "ProposalComment") {
                            newRow = bell.newProposalCommentRow(notification)
                        }
                        if (newRow) {
                            newRow.attr("data-reason", reason);
                            $(".bell-details").prepend(newRow);
                        }
                    }
                };

                var $count = $(".header-bell__count, .side-bell__count");
                var $img = $(".header-bell__img");
                var $noNew = $(".bell-details__no-new");

                bell.renewCount = function () {
                    Codeforces.reformatTimes();
                    var count = 0;
                    var counted = {};
                    for (var id in bell.notifications)
                        if (bell.notifications.hasOwnProperty(id)) {
                            var notification = bell.notifications[id];
                            if (notification) {
                                var reason = notification.data.reasonType + "#" + notification.reasonId;
                                if (!notification.read && !counted[reason]) {
                                    count++;
                                    counted[reason] = true;
                                }
                            }
                        }
                    if (count > 0) {
                        $count.text(count);
                        $img.addClass("header-bell__img-active");
                        $noNew.hide();
                    } else {
                        $count.text("");
                        $img.removeClass("header-bell__img-active");
                        if ($bellDetails.css("display") != "block") {
                            $noNew.show();
                        }
                    }
                };

                for (var id in bell.notifications)
                    if (bell.notifications.hasOwnProperty(id))
                        bell.renderRow(bell.notifications[id]);

                if (window.eventCatcher) {
                    window.eventCatcher.subscribe(Codeforces.getUserChannel(), function(json) {
                        if (json.t === "n") {
                            for (var i in json.d)
                                if (json.d.hasOwnProperty(i)) {
                                    var notification = json.d[i];
                                    notification.data = JSON.parse(notification.dataJson);
                                    bell.notifications[notification.id] = notification;
                                    bell.renderRow(notification);
                                    bell.renewCount();
                                }
                        }
                    });
                }


                $(".header-bell, .side-bell").click(function() {
                    if ($bellDetails.css("display") == "none") {
                        for (var id in bell.notifications)
                            if (bell.notifications.hasOwnProperty(id))
                                if (bell.notifications[id].read)
                                    bell.renderRow(bell.notifications[id]);
                        bell.renewCount();

                        $bellDetails.css("display", "block");

                        var notificationIds = [];
                        for (id in bell.notifications)
                            if (bell.notifications.hasOwnProperty(id))
                                notificationIds.push(id);

                        setTimeout(function() {
                            if ($bellDetails.css("display") == "block") {
                                $.post("/data/notification", {action: "markAsRead", notificationIds: notificationIds.join(",")}, function(result) {
                                    // No operations.
                                }, "json");

                                for (var i in notificationIds) {
                                    if (notificationIds.hasOwnProperty(i)) {
                                        bell.notifications[notificationIds[i]].read = true;
                                    }
                                }
                                bell.renewCount();
                            }
                        }, 2000);
                    } else {
                        var has = false;
                        for (var id in bell.notifications) {
                            if (bell.notifications.hasOwnProperty(id) && !bell.notifications[id].read) {
                                has = true;
                            }
                        }
                        if (!has) {
                            $(".side-bell").css("visibility", "hidden");
                        }
                        $bellDetails.css("display", "none");
                        bell.renewCount();
                    }
                });
            }

            bell = new Bell();
            bell.renewCount();
        })
    </script>

    <div id="header" style="position: relative;">
        <div style="float:left;">
            <a href="/"><img alt="Codeforces" title="Codeforces" src="//sta.codeforces.com/s/76577/images/codeforces-logo-with-telegram.png"/></a>
        </div>
        <div class="lang-chooser">
            <div style="text-align: right;">
                <a href="?locale=en"><img src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" title="In English" alt="In English"/></a>
                <a href="?locale=ru"><img src="//sta.codeforces.com/s/76577/images/flags/24/ru.png" title="По-русски" alt="По-русски"/></a>
            </div>

            <div >
                <a href="/profile/2018030402055">2018030402055</a>
                |
                <a href="/6df06dd6640825d86fd74250d9016eef/logout">Logout</a>

            </div>



        </div>
        <br style="clear: both;"/>
    </div>


    <div class="roundbox menu-box" style="">
        <div class="roundbox-lt">&nbsp;</div>
        <div class="roundbox-rt">&nbsp;</div>
        <div class="roundbox-lb">&nbsp;</div>
        <div class="roundbox-rb">&nbsp;</div>
        <div class="menu-list-container">
            <ul class="menu-list main-menu-list">
                <li class="current"><a href="/">Home</a></li>
                <li class=""><a href="/top">Top</a></li>
                <li class=""><a href="/contests">Contests</a></li>
                <li class=""><a href="/gyms">Gym</a></li>
                <li class=""><a href="/problemset">Problemset</a></li>
                <li class=""><a href="/groups/my">Groups</a></li>
                <li class=""><a href="/ratings">Rating</a></li>
                <li class=""><a href="/edu/courses"><span class="edu-menu-item">Edu</span></a></li>
                <li class=""><a href="/apiHelp">API</a></li>
                <li class=""><a href="/calendar">Calendar</a></li>
                <li class=""><a href="/help">Help</a></li>
            </ul>
            <form method="post" action="/search"><input type='hidden' name='csrf_token' value='9712b77f90ae1bf64920f4bd65248fab'/>
                <input class="search" name="query" data-isPlaceholder="true" value=""/>
            </form>
            <br style="clear: both;"/>
        </div>

    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            $("input.search").focus(function () {
                if ($(this).attr("data-isPlaceholder") === "true") {
                    $(this).val("");
                    $(this).removeAttr("data-isPlaceholder");
                }
            });
        });
    </script>
    <div style="margin:1em;text-align: center; position: relative;" class="alert alert-success" data-infoBarId="25">
        <i class="icon-info"></i> Please subscribe to the official Codeforces channel in Telegram via the link <a href="https://t.me/codeforces_official">https://t.me/codeforces_official</a>.
        <span class="infobar-close" style="position: absolute; top: 0.2em; right: 0.3em; cursor: pointer; font-size: 1.4em;">&times;</span>
    </div>
    <script type="text/javascript">
        $(document).ready(function () {
            $(".infobar-close").click(function () {
                $(this).parent().fadeOut();
                $.post("/data/infobars", {action: "hide", infoBarId: $(this).parent().attr("data-infoBarId")}, function (response) {
                    // No operations.
                }, "json");
            });
        });
    </script>
    <br style="height: 3em; clear: both;"/>

    <div style="position: relative;">
        <div id="sidebar">
            <div class="roundbox sidebox" style="">
                <div class="roundbox-lt">&nbsp;</div>
                <div class="roundbox-rt">&nbsp;</div>
                <div class="caption titled">&rarr; Pay attention
                    <div class="top-links">
                    </div>
                </div>
                <div style="padding: 0.5em;">
                    <div style="text-align:center;border-bottom: 1px solid rgb(185, 185, 185);margin:0 -0.5em 0.5em -0.5em;padding: 0 1em 0.5em 1em;">
                        <span class='contest-state-phase'>Before contest</span><br/><a href="/contests/1483,1484">Codeforces Round #709 (Div. 1, based on Technocup 2021 Final Round)</a><br/><span class='countdown' home='//sta.codeforces.com/s/76577' noRedirection='true' textBeforeRedirect=''><span title="50:44:14">2 days</span></span>
                    </div>
                    <div style="text-align:center;border-bottom: 1px solid rgb(185, 185, 185);margin:0 -0.5em 0.5em -0.5em;padding: 0 1em 0.5em 1em;">
                        <span class='contest-state-phase'>Before contest</span><br/><a href="/contests/1483,1484">Codeforces Round #709 (Div. 2, based on Technocup 2021 Final Round)</a><br/><span class='countdown' home='//sta.codeforces.com/s/76577' noRedirection='true' textBeforeRedirect=''><span title="50:44:14">2 days</span></span>
                    </div>
                    <div style="text-align:center;">
                        <div class="socials" style="text-align: left;">
                            <div style="margin-top: 0.85em;position: relative;">

                                <div id="fb-root"></div>
                                <script>
                                    (function(d, s, id) {
                                        var js, fjs = d.getElementsByTagName(s)[0];
                                        if (d.getElementById(id)) return;
                                        js = d.createElement(s); js.id = id;
                                        js.src = "//connect.facebook.net/en_EN/all.js#appId=554666954583323&xfbml=1";
                                        fjs.parentNode.insertBefore(js, fjs);
                                    }(document, 'script', 'facebook-jssdk'));
                                </script>
                                <div class="fb-like" data-href="//codeforces.com/contests/1483,1484" data-send="false" data-width="256" data-show-faces="false" data-font="arial" data-ref='1723700461'></div>

                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <script> $(function () {

            }); </script>

            <style type="text/css">
                ._StreamsSidebarFrame_frame {
                    padding: 0.5em 0.5em 0 0.5em;
                }
                ._StreamsSidebarFrame_frame ._StreamsSidebarFrame_stream {
                    text-align: center;
                    border-bottom: 1px solid #b9b9b9;
                    margin: 0 -0.5em 0.5em -0.5em;
                    padding: 0 1em 0.5em 1em;
                }
                ._StreamsSidebarFrame_frame ._StreamsSidebarFrame_stream ._StreamsSidebarFrame_user {
                    margin: 0 0 0.5em 0;
                    font-size: 0.8em;
                }
                ._StreamsSidebarFrame_frame ._StreamsSidebarFrame_stream ._StreamsSidebarFrame_timeMark {
                    color: #777;
                    font-size: 0.9em;
                }
                ._StreamsSidebarFrame_frame ._StreamsSidebarFrame_stream:last-of-type {
                    border-bottom: unset;
                    margin: unset;
                    padding: 0 0.5em 0.5em 1em;
                }
                ._StreamsSidebarFrame_frame ._viewAll {
                    text-align: right;
                }
                .moreRunningStreamsSidebarNote {
                    color: #777 !important;
                    text-decoration: none;
                }
            </style>

            <style>
                .personal-sidebar {
                    margin: 1em;
                }

                .personal-sidebar .for-avatar {
                    float: right;
                }

                .personal-sidebar li {
                    font-size: 0.8em;
                }

                .personal-sidebar .nav-links {
                    margin-top: 0.5em;
                }

                .personal-sidebar .nav-links li {
                    list-style: disc;
                    list-style-position: inside;
                }

                .personal-sidebar .propertyLinks li img {
                    margin-right: 0.5em;
                }
            </style>

            <div class="roundbox sidebox" style="">
                <div class="roundbox-lt">&nbsp;</div>
                <div class="roundbox-rt">&nbsp;</div>
                <div class="caption titled">&rarr; 2018030402055
                    <div class="top-links">
                    </div>
                </div>
                <div class="personal-sidebar">
                    <div class="for-avatar"><div class="avatar"><a href="/profile/2018030402055" style="position: relative;"><img src='//userpic.codeforces.com/802869/avatar/24e07a5b3990a5ff.jpg'/></a><div><a href="/profile/2018030402055" title="Expert 2018030402055" class="rated-user user-blue">2018030402055</a></div></div></div><div><ul class="propertyLinks"><li><img style="vertical-align:middle;position:relative;top:-2px" src="//sta.codeforces.com/s/76577/images/icons/rating-16x16.png"
                                                                                                                                                                                                                                                                                                                                                                                    alt="User''s contest rating in Codeforces community" title="User''s contest rating in Codeforces community"
                />Rating:&nbsp<span style="font-weight:bold;" class="user-blue">1705</span></li><li><img style="vertical-align:middle;position:relative;top:-2px" src="//sta.codeforces.com/s/76577/images/icons/star_blue_16.png"
                                                                                                         alt="User''s contribution into Codeforces community" title="User''s contribution into Codeforces community"
                />Contribution:&nbsp<span style="color:green;font-weight:bold;">+1</span></li></ul><ul class="nav-links"><li><a href="/settings/general">Settings</a></li><li><a href="/blog/2018030402055">Blog</a></li><li><a href="/teams">Teams</a></li><li><a href="/submissions/2018030402055">Submissions</a></li><li><a href="/usertalk">Talks</a></li><li><a href="/contests/with/2018030402055">Contests</a></li></ul></div><br style="clear:both;"/>    </div>
            </div>
            <div class="roundbox sidebox top-contributed" style="">
                <div class="roundbox-lt">&nbsp;</div>
                <div class="roundbox-rt">&nbsp;</div>
                <div class="caption titled">&rarr; Top rated
                    <div class="top-links">
                    </div>
                </div>
                <table class="rtable ">
                    <tbody>
                    <tr>
                        <th class="left" style="width:2.25em;">#</th>
                        <th class="">User</th>
                        <th class="" style="width:5em;">Rating</th>
                    </tr>
                    <tr>
                        <td class="left dark">1</td>
                        <td class=" dark"><a href="/profile/tourist" title="Legendary Grandmaster tourist" class="rated-user user-legendary"><span class="legendary-user-first-letter">t</span>ourist</a></td>
                        <td class=" dark">3668</td>
                    </tr>
                    <tr>
                        <td class="left ">2</td>
                        <td class=""><a href="/profile/maroonrk" title="Legendary Grandmaster maroonrk" class="rated-user user-legendary"><span class="legendary-user-first-letter">m</span>aroonrk</a></td>
                        <td class="">3564</td>
                    </tr>
                    <tr>
                        <td class="left dark">3</td>
                        <td class=" dark"><a href="/profile/Benq" title="Legendary Grandmaster Benq" class="rated-user user-legendary"><span class="legendary-user-first-letter">B</span>enq</a></td>
                        <td class=" dark">3540</td>
                    </tr>
                    <tr>
                        <td class="left ">4</td>
                        <td class=""><a href="/profile/Petr" title="Legendary Grandmaster Petr" class="rated-user user-legendary"><span class="legendary-user-first-letter">P</span>etr</a></td>
                        <td class="">3519</td>
                    </tr>
                    <tr>
                        <td class="left dark">5</td>
                        <td class=" dark"><a href="/profile/ecnerwala" title="Legendary Grandmaster ecnerwala" class="rated-user user-legendary"><span class="legendary-user-first-letter">e</span>cnerwala</a></td>
                        <td class=" dark">3403</td>
                    </tr>
                    <tr>
                        <td class="left ">6</td>
                        <td class=""><a href="/profile/Radewoosh" title="Legendary Grandmaster Radewoosh" class="rated-user user-legendary"><span class="legendary-user-first-letter">R</span>adewoosh</a></td>
                        <td class="">3374</td>
                    </tr>
                    <tr>
                        <td class="left dark">7</td>
                        <td class=" dark"><a href="/profile/jiangly" title="Legendary Grandmaster jiangly" class="rated-user user-legendary"><span class="legendary-user-first-letter">j</span>iangly</a></td>
                        <td class=" dark">3355</td>
                    </tr>
                    <tr>
                        <td class="left ">8</td>
                        <td class=""><a href="/profile/scott_wu" title="Legendary Grandmaster scott_wu" class="rated-user user-legendary"><span class="legendary-user-first-letter">s</span>cott_wu</a></td>
                        <td class="">3313</td>
                    </tr>
                    <tr>
                        <td class="left dark">9</td>
                        <td class=" dark"><a href="/profile/ainta" title="Legendary Grandmaster ainta" class="rated-user user-legendary"><span class="legendary-user-first-letter">a</span>inta</a></td>
                        <td class=" dark">3298</td>
                    </tr>
                    <tr>
                        <td class="left bottom">10</td>
                        <td class="bottom"><a href="/profile/Miracle03" title="Legendary Grandmaster Miracle03" class="rated-user user-legendary"><span class="legendary-user-first-letter">M</span>iracle03</a></td>
                        <td class="bottom">3294</td>
                    </tr>
                    </tbody>
                </table>
                <div class="bottom-links">
                    <table style="width:100%;">
                        <tbody>
                        <tr>
                            <td style="text-align:left;">
                                <a href="/ratings/countries">Countries</a> |
                                <a href="/ratings/cities">Cities</a> |
                                <a href="/ratings/organizations">Organizations</a>
                            </td>
                            <td style="text-align:right;">
                                <a href="/ratings">View all &rarr;</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div><div class="roundbox sidebox top-contributed" style="">
            <div class="roundbox-lt">&nbsp;</div>
            <div class="roundbox-rt">&nbsp;</div>
            <div class="caption titled">&rarr; Top contributors
                <div class="top-links">
                </div>
            </div>
            <table class="rtable ">
                <tbody>
                <tr>
                    <th class="left" style="width:2.25em;">#</th>
                    <th class="">User</th>
                    <th class="" style="width:5em;">Contrib.</th>
                </tr>
                <tr>
                    <td class="left  dark">1</td>
                    <td class=" dark"><a href="/profile/1-gon" title="Grandmaster 1-gon" class="rated-user user-red">1-gon</a></td>
                    <td class=" dark">197</td>
                </tr>
                <tr>
                    <td class="left ">2</td>
                    <td class=""><a href="/profile/Errichto" title="International Grandmaster Errichto" class="rated-user user-red">Errichto</a></td>
                    <td class="">193</td>
                </tr>
                <tr>
                    <td class="left  dark">3</td>
                    <td class=" dark"><a href="/profile/rng_58" title="Legendary Grandmaster rng_58" class="rated-user user-legendary"><span class="legendary-user-first-letter">r</span>ng_58</a></td>
                    <td class=" dark">188</td>
                </tr>
                <tr>
                    <td class="left ">4</td>
                    <td class=""><a href="/profile/awoo" title="Grandmaster awoo" class="rated-user user-red">awoo</a></td>
                    <td class="">187</td>
                </tr>
                <tr>
                    <td class="left  dark">5</td>
                    <td class=" dark"><a href="/profile/SecondThread" title="Grandmaster SecondThread" class="rated-user user-red">SecondThread</a></td>
                    <td class=" dark">185</td>
                </tr>
                <tr>
                    <td class="left ">6</td>
                    <td class=""><a href="/profile/Um_nik" title="Legendary Grandmaster Um_nik" class="rated-user user-legendary"><span class="legendary-user-first-letter">U</span>m_nik</a></td>
                    <td class="">183</td>
                </tr>
                <tr>
                    <td class="left  dark">7</td>
                    <td class=" dark"><a href="/profile/vovuh" title="Master vovuh" class="rated-user user-orange">vovuh</a></td>
                    <td class=" dark">176</td>
                </tr>
                <tr>
                    <td class="left ">7</td>
                    <td class=""><a href="/profile/Ashishgup" title="Grandmaster Ashishgup" class="rated-user user-red">Ashishgup</a></td>
                    <td class="">176</td>
                </tr>
                <tr>
                    <td class="left  dark">9</td>
                    <td class=" dark"><a href="/profile/antontrygubO_o" title="International Grandmaster antontrygubO_o" class="rated-user user-red">antontrygubO_o</a></td>
                    <td class=" dark">175</td>
                </tr>
                <tr>
                    <td class="left bottom">10</td>
                    <td class="bottom"><a href="/profile/-is-this-fft-" title="Grandmaster -is-this-fft-" class="rated-user user-red">-is-this-fft-</a></td>
                    <td class="bottom">173</td>
                </tr>
                </tbody>
            </table>
            <div class="bottom-links">
                <table style="width:100%;">
                    <tbody>
                    <tr>
                        <td style="text-align:left;">
                        </td>
                        <td style="text-align:right;">
                            <a href="/top-contributed">View all &rarr;</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

            <script type="text/javascript">
                $(document).ready(function () {
                });
            </script>

            <div class="roundbox sidebox" style="">
                <div class="roundbox-lt">&nbsp;</div>
                <div class="roundbox-rt">&nbsp;</div>
                <div class="caption titled">&rarr; Find user
                    <div class="top-links">
                    </div>
                </div>
                <form class="handleForm" method="post"><input type='hidden' name='csrf_token' value='9712b77f90ae1bf64920f4bd65248fab'/>
                    <div style="padding:1em;text-align:right;">
                        <label style="padding-right:1em;">Handle:
                            <input style="width:12em;" type="text" class="handleBox"/>
                        </label>
                    </div>
                    <div style="padding: 0 1em 1em 1em;text-align:right;">
                        <input style="height:1.65em;padding:0 0.75em;" type="submit" value="Find"/>
                    </div>
                </form>
            </div>
            <script type="text/javascript">
                $(document).ready(function () {
                    $(".handleBox").autocomplete("/data/handles", {
                        delay: 200,
                        width: 200,
                        selectFirst: false,
                        matchContains: true,
                        minChars: 3
                    });
                    $(".handleForm").attr("autocomplete", "off").submit(function () {
                        var link = "/profile/userHandle".replace(
                            "userHandle", $(this).find(".handleBox").val()
                        );
                        window.location = link;
                        return false;
                    });
                });
            </script>


            <div class="roundbox sidebox" style="">
                <div class="roundbox-lt">&nbsp;</div>
                <div class="roundbox-rt">&nbsp;</div>
                <div class="caption titled">&rarr; Recent actions
                    <div class="top-links">
                    </div>
                </div>
                <div class="recent-actions">
                    <ul>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/awoo" title="Grandmaster awoo" class="rated-user user-red">awoo</a>        &rarr;
                            <a href="/blog/entry/88744">Educational Codeforces Round 106 [Rated for Div. 2]</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov" class="rated-user user-admin">MikeMirzayanov</a>        &rarr;
                            <a href="/blog/entry/8790">Rule about third-party code is changing</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                            <img alt="Necropost" title="Necropost" src="//sta.codeforces.com/s/76577/images/icons/hourglass.png"  style="vertical-align:middle; position: relative; top: 1px;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/risingStark" title="Pupil risingStark" class="rated-user user-green">risingStark</a>        &rarr;
                            <a href="/blog/entry/88751">How to solve problems of this kind?</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/awoo" title="Grandmaster awoo" class="rated-user user-red">awoo</a>        &rarr;
                            <a href="/blog/entry/88344">Educational Codeforces Round 105 Editorial</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/mansigpt" title="Expert mansigpt" class="rated-user user-blue">mansigpt</a>        &rarr;
                            <a href="/blog/entry/88729">Congrats Um_nik </a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/shishin" title="Expert shishin" class="rated-user user-blue">shishin</a>        &rarr;
                            <a href="/blog/entry/88677">Codeforces Round #708 Editorial</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/Ripatti" title="Master Ripatti" class="rated-user user-orange">Ripatti</a>        &rarr;
                            <a href="/blog/entry/3140">Editorial for Codeforces Beta Round #93</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                            <img alt="Necropost" title="Necropost" src="//sta.codeforces.com/s/76577/images/icons/hourglass.png"  style="vertical-align:middle; position: relative; top: 1px;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/K_B_G" title="Specialist K_B_G" class="rated-user user-cyan">K_B_G</a>        &rarr;
                            <a href="/blog/entry/88796">issue regarding problem D edu round 106</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/Supermagzzz" title="Master Supermagzzz" class="rated-user user-orange">Supermagzzz</a>        &rarr;
                            <a href="/blog/entry/86406">Codeforces Round #693 (Div. 3) Editorial</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/arham_doshi" title="Expert arham_doshi" class="rated-user user-blue">arham_doshi</a>        &rarr;
                            <a href="/blog/entry/79518">cses graph session editorial(incomplete)</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/vovuh" title="Master vovuh" class="rated-user user-orange">vovuh</a>        &rarr;
                            <a href="/blog/entry/47890">Codeforces Round #377 (Div. 2) Editorial</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                            <img alt="Necropost" title="Necropost" src="//sta.codeforces.com/s/76577/images/icons/hourglass.png"  style="vertical-align:middle; position: relative; top: 1px;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/AghaTizi" title="Grandmaster AghaTizi" class="rated-user user-red">AghaTizi</a>        &rarr;
                            <a href="/blog/entry/63257">Dp On Trees</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                            <img alt="Necropost" title="Necropost" src="//sta.codeforces.com/s/76577/images/icons/hourglass.png"  style="vertical-align:middle; position: relative; top: 1px;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov" class="rated-user user-admin">MikeMirzayanov</a>        &rarr;
                            <a href="/blog/entry/88691">Codeforces: Results of 2020 [list some changes and improvements]</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/wuyanyidui" title="Expert wuyanyidui" class="rated-user user-blue">wuyanyidui</a>        &rarr;
                            <a href="/blog/entry/88792">I found two copies of the same code in div2 yesterday</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/sigma_g" title="Master sigma_g" class="rated-user user-orange">sigma_g</a>        &rarr;
                            <a href="/blog/entry/65477">Codeforces dark theme</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                            <img alt="Necropost" title="Necropost" src="//sta.codeforces.com/s/76577/images/icons/hourglass.png"  style="vertical-align:middle; position: relative; top: 1px;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/Imakf" title="Master Imakf" class="rated-user user-orange">Imakf</a>        &rarr;
                            <a href="/blog/entry/88533">Codeforces Round #706 Editorial</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/hmehta" title="Unrated, hmehta" class="rated-user user-black">hmehta</a>        &rarr;
                            <a href="/blog/entry/88791">Topcoder SRM 802</a>
                            &nbsp;&nbsp;<img alt="Text created or updated" title="Text created or updated" src="//sta.codeforces.com/s/76577/images/icons/x-update-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/hmehta" title="Unrated, hmehta" class="rated-user user-black">hmehta</a>        &rarr;
                            <a href="/blog/entry/88726">2021 Humblefool Cup w/ $600 in Prizes</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/_Shiva_99" title="Newbie _Shiva_99" class="rated-user user-gray">_Shiva_99</a>        &rarr;
                            <a href="/blog/entry/88788">NEED HELP IN [C. Watto and Mechanism] PROBLEM</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/prashantpal0388" title="Specialist prashantpal0388" class="rated-user user-cyan">prashantpal0388</a>        &rarr;
                            <a href="/blog/entry/88236">can anyone suggest me how to solve the problem bracket sequences II from cses problemset?</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/brunomont" title="Master brunomont" class="rated-user user-orange">brunomont</a>        &rarr;
                            <a href="/blog/entry/83969">[Tutorial] A powerful representation of integer sets</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/rish-bish" title="Newbie rish-bish" class="rated-user user-gray">rish-bish</a>        &rarr;
                            <a href="/blog/entry/88768">UPDATE: Added Atcoder and Codeforces Questions to Caucus</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/dominator1234" title="Expert dominator1234" class="rated-user user-blue">dominator1234</a>        &rarr;
                            <a href="/blog/entry/88785">Problem D Educational Round 106</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/Hoa_Dau_Biec" title="Specialist Hoa_Dau_Biec" class="rated-user user-cyan">Hoa_Dau_Biec</a>        &rarr;
                            <a href="/blog/entry/88780">Help needed in Substring reversals on CSES</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                        <li><div style="font-size:0.9em;padding:0.5em 0;">
                            <a href="/profile/errorgorn" title="Grandmaster errorgorn" class="rated-user user-red">errorgorn</a>        &rarr;
                            <a href="/blog/entry/88248">Codeforces Global Round 13 Editorial</a>
                            &nbsp;&nbsp;<img alt="New comment(s)" title="New comment(s)" src="//sta.codeforces.com/s/76577/images/icons/comment-12x12.png" style="vertical-align:middle;"/>
                        </div>
                        </li>
                    </ul>
                </div>
                <div class="bottom-links">
                    <table style="width:100%;">
                        <tbody>
                        <tr>
                            <td style="text-align:left;">
                            </td>
                            <td style="text-align:right;">
                                <a href="/recent-actions">Detailed &rarr;</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div></div>
        <div id="pageContent" class="content-with-sidebar">
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="89290">
                    <div class="title">
                        <a href="/blog/entry/88744">            <p>Educational Codeforces Round 106 [Rated for Div. 2]</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/awoo" title="Grandmaster awoo" class="rated-user user-red">awoo</a>,
                        <a href="/topic/89290/en3">history</a>,
                        <span class="format-humantime" title="Mar/17/2021 17:09">44 hours ago</span>,
                        translation,
                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello Codeforces!</p><p>On <a class="contest-time" contestid="1499" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=18&amp;month=3&amp;year=2021&amp;hour=17&amp;min=50&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/18/2021 17:50 (Moscow time)</span></a> <a href="/contest/1499" title="Educational Codeforces Round 106 (Rated for Div. 2)">Educational Codeforces Round 106 (Rated for Div. 2)</a> will start.</p><p>Series of Educational Rounds continue being held as <a href="https://harbour.space/">Harbour.Space University</a> initiative! You can read the details about the cooperation between <a href="https://harbour.space/">Harbour.Space University</a> and Codeforces in the <a href="//codeforces.com/blog/entry/51208">blog post</a>.</p><p>This round will be <strong>rated for the participants with rating lower than 2100</strong>. It will be held on extended ICPC rules. The penalty for each incorrect submission until the submission with a full solution is 10 minutes. After the end of the contest you will have 12 hours to hack any solution you want. You will have access to copy any solution and test it locally.</p><p>You will be given <strong>6 or 7 problems</strong> and <strong>2 hours</strong> to solve them.</p><p>The problems were invented and prepared by Roman <a class="rated-user user-orange" href="/profile/Roms" title="Master Roms">Roms</a> Glazov, Adilbek <a class="rated-user user-orange" href="/profile/adedalic" title="International Master adedalic">adedalic</a> Dalabaev, Vladimir <a class="rated-user user-orange" href="/profile/vovuh" title="Master vovuh">vovuh</a> Petrov, Ivan <a class="rated-user user-red" href="/profile/BledDest" title="International Grandmaster BledDest">BledDest</a> Androsov, Maksim <a class="rated-user user-violet" href="/profile/Neon" title="Candidate Master Neon">Neon</a> Mescheryakov and me. Also huge thanks to Mike <a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> Mirzayanov for great systems Polygon and Codeforces.</p><p>Good luck to all the participants!</p><p>Our friends at Harbour.Space also have a message for you:</p><p><img alt="Codeforces and Harbour.Space" src="/predownloaded/ef/71/ef710cce04801381ec2738aec44da8aa4b29c2eb.png" style="height: 200.0px;margin: 10.0px;max-width: 100.0%;max-height: 100.0%;" /></p><p><em>Dear Codeforces!</em> </p><p><em>We are coming with another scholarship opportunity to share with you. This time, our scholarship is targeted towards the brightest women in the community.</em> </p><p><em>As you might know, March is the month where the whole world celebrates women. At Harbour.Space we want to use this opportunity to encourage more women to join the tech world and challenge the gender-bias in this field.</em> </p><p><em>We believe that gender equality in the workplace starts with gender equality in the classroom. For that reason, we are offering our <strong>Women in Tech Scholarship</strong>. The scholarship consists of:</em></p> <ul>   <li><em>50% off the yearly tuition fee: covers around €29,000 for bachelors and €11,450 for masters.</em></li>   <li><em>32% off the application fee: €85 instead of €125</em></li> </ul><p><em>You can find more information about the scholarship here.</em></p> <center style="margin: 2.5em;"> <a href="https://iwd-scholarship.harbour.space/?utm_source=codeforces&amp;utm_medium=referral&amp;utm_campaign=iwd&amp;utm_content=" style="text-decoration: none;font-size: 16.0px;background-color: rgb(82,57,150);color: white;font-weight: bold;padding: 0.5em 1.0em;">MORE INFO→</a> </center><p><img alt="Harbour.Space" src="/predownloaded/a4/4d/a44d6a6962ef8b9fa173b1b43adff828bd184e6e.png" style="height: 200.0px;margin: 10.0px;max-width: 100.0%;max-height: 100.0%;" /></p><p><em><strong>Make sure to apply before March 31st to benefit from the scholarship and discount.</strong></em> </p><p><em>Don’t hesitate to share this opportunity with any bright women in your personal circle as well. A simple share can help us transform someone's life.</em> </p><p><em>We are always happy to see Codeforce members join the Harbour.Space family.</em> </p><p><em>Keep in touch and follow us on <a href="https://www.linkedin.com/school/harbour-space/">LinkedIn</a> for more scholarship opportunities. And follow us on <a href="https://www.instagram.com/harbour.space/">Instagram</a> to stay in touch with our student life, events, and success stories from our students.</em></p><p><em>Good luck on your round, and see you next time!</em> </p><p><em>Harbour.Space University</em> </p></div><p><a href="/blog/entry/88744">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1499" class="notice" style="text-decoration: none;">Educational Codeforces Round 106 (Rated for Div. 2)</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-42750-89290").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "42750",
                                        blogEntryId: "88744",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>



                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-89290"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+169</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-89290"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/awoo"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                         alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/awoo">
                                        awoo
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/17/2021 17:09">44 hours ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88744#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88744#comments">
                                        354
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-89290").click(function () {
                                $.post("/data/topic/vote", {topicId: 89290, _tta: Codeforces.tta(), topicRevisionId: 220800, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-89290").click(function () {
                                $.post("/data/topic/vote", {topicId: 89290, _tta: Codeforces.tta(), topicRevisionId: 220800, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="89226">
                    <div class="title">
                        <a href="/blog/entry/88680">            <p>Codeforces Round #708</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/shishin" title="Expert shishin" class="rated-user user-blue">shishin</a>,
                        <a href="/topic/89226/en6">history</a>,
                        <span class="format-humantime" title="Mar/14/2021 15:08">5 days ago</span>,
                        translation,
                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello again, Codeforces!</p><p><a class="rated-user user-orange" href="/profile/Artyom123" title="Master Artyom123">Artyom123</a> and I are happy to invite you to <a href="/contest/1497" title="Codeforces Round 708 (Div. 2)">Codeforces Round #708 (Div. 2)</a>, which will take place on <a class="contest-time" contestid="1497" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=17&amp;month=3&amp;year=2021&amp;hour=17&amp;min=35&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/17/2021 17:35 (Moscow time)</span></a>. <strong>This round will be rated for the participants with rating lower than 2100</strong></p><p>These are some awesome people that we would like to thank:</p> <ul>   <li><a class="rated-user user-red" href="/profile/isaf27" title="International Grandmaster isaf27">isaf27</a> for round coordination and helpful ideas</li>   <li><a class="rated-user user-orange" href="/profile/physics0523" title="Master physics0523">physics0523</a>, <a class="rated-user user-orange" href="/profile/Kotehok3" title="Master Kotehok3">Kotehok3</a>, <a class="rated-user user-violet" href="/profile/Ziware" title="Candidate Master Ziware">Ziware</a>, <a class="rated-user user-red" href="/profile/Kirill22" title="Grandmaster Kirill22">Kirill22</a>, <a class="rated-user user-blue" href="/profile/Absyarka" title="Expert Absyarka">Absyarka</a>, <a class="rated-user user-red" href="/profile/AliShahali1382" title="International Grandmaster AliShahali1382">AliShahali1382</a>, <a class="rated-user user-violet" href="/profile/not_tehlka" title="Candidate Master not_tehlka">not_tehlka</a>, <a class="rated-user user-orange" href="/profile/Mohammad_Yasser" title="Master Mohammad_Yasser">Mohammad_Yasser</a>, <a class="rated-user user-orange" href="/profile/FairyWinx" title="Master FairyWinx">FairyWinx</a>, <a class="rated-user user-blue" href="/profile/kassutta" title="Expert kassutta">kassutta</a>, <a class="rated-user user-blue" href="/profile/LazyPrime" title="Expert LazyPrime">LazyPrime</a>, <a class="rated-user user-blue" href="/profile/rkm62" title="Expert rkm62">rkm62</a> for testing and providing feedback</li>   <li><a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> for beloved platforms Codeforces and Polygon.</li> </ul><p>You will have <strong>2 hours</strong> to solve <strong>5 problems (and 2 subtasks)</strong>. This time the statements are not related to Valorant, but we still play it sometimes.</p><p>We hope that you will like all problems and you won't face any troubles during the contest. Good luck!</p><p>The scoring distribution: <strong>500</strong> — <strong>750</strong> — <strong>(750 + 500)</strong> — <strong>1750</strong> — <strong>(1500 + 1500)</strong></p><p><strong>UPD:</strong> <a href="https://codeforces.com/blog/entry/88677">Editorial</a></p></div><p><a href="/blog/entry/88680">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1497" class="notice" style="text-decoration: none;">Codeforces Round #708 (Div. 2)</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-99522-89226").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "99522",
                                        blogEntryId: "88680",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>



                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-89226"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+741</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-89226"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/shishin"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                            alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/shishin">
                                        shishin
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/14/2021 15:08">5 days ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88680#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88680#comments">
                                        218
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-89226").click(function () {
                                $.post("/data/topic/vote", {topicId: 89226, _tta: Codeforces.tta(), topicRevisionId: 220618, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-89226").click(function () {
                                $.post("/data/topic/vote", {topicId: 89226, _tta: Codeforces.tta(), topicRevisionId: 220618, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="89237">
                    <div class="title">
                        <a href="/blog/entry/88691">            <p>Codeforces: Results of 2020 [list some changes and improvements]</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov" class="rated-user user-admin">MikeMirzayanov</a>,
                        <span class="format-humantime" title="Mar/14/2021 20:24">5 days ago</span>,

                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello, Codeforces!</p><p>I understand that 2021 has been going on for a long time, but here I have picked up a subset of some of the improvements that the Codeforces team made in 2020. Soon I will publish numbers (no, charts) with statistics for 2020. In the meantime, I bring to your attention a list of changes and improvements.</p><p>By the way, this is a decent list. This is about half to a quarter of all changes. It's just that other changes are more often somewhere in the internals of the system and are not visible to users. Please read this list. Each item is the effort of someone from the team. Thanks to <a class="rated-user user-admin" href="/profile/geranazavr555" title="Headquarters, geranazavr555">geranazavr555</a>, <a class="rated-user user-orange" href="/profile/kuviman" title="Master kuviman">kuviman</a> and <a class="rated-user user-admin" href="/profile/cannor147" title="Headquarters, cannor147">cannor147</a> for their efforts. You've made our platform better! Well, by the way, I don't quit programming and many improvements were made by me.</p><p>The items on the list are written in a concise and informal form, many of the items I just copied from commit messages from git. If you want more details — ask in the comments, we will tell you! Of course, I forgot to include some improvements in the list.</p></div><p><a href="/blog/entry/88691">Read more »</a></p>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-1-89237").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "1",
                                        blogEntryId: "88691",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>


                    <div style="font-size: 1.1rem;line-height: 11px;">
                        <img style="vertical-align: middle;" src="//sta.codeforces.com/s/76577/images/blog/tags.png" title="Tags" alt="Tags"/>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=codeforces" class="tag notice" style="text-decoration: none;">codeforces</a>,
                </span>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=2020" class="tag notice" style="text-decoration: none;">2020</a>,
                </span>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=results" class="tag notice" style="text-decoration: none;">results</a>
                </span>
                    </div>

                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-89237"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+1382</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-89237"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/MikeMirzayanov"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                                   alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/MikeMirzayanov">
                                        MikeMirzayanov
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/14/2021 20:24">5 days ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88691#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88691#comments">
                                        57
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-89237").click(function () {
                                $.post("/data/topic/vote", {topicId: 89237, _tta: Codeforces.tta(), topicRevisionId: 220454, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-89237").click(function () {
                                $.post("/data/topic/vote", {topicId: 89237, _tta: Codeforces.tta(), topicRevisionId: 220454, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="89136">
                    <div class="title">
                        <a href="/blog/entry/88590">            <p>Codeforces Round #707 (Div.1, Div.2, based on Moscow Open Olympiad in Informatics, rated)</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/ch_egor" title="International Grandmaster ch_egor" class="rated-user user-red">ch_egor</a>,
                        <span class="format-humantime" title="Mar/12/2021 00:04">8 days ago</span>,
                        translation,
                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello!</p><p>Right now happens the first tour of the Open Olympiad in Informatics, and tomorrow will be the second one. This contest is prepared by Moscow Olympiad Scientific Committee that you may know by Moscow Team Olympiad, Moscow Olympiad for Young Students and Metropolises Olympiad (rounds <a href="https://codeforces.com/blog/entry/21185">327</a>, <a href="https://codeforces.com/blog/entry/23309">342</a>, <a href="https://codeforces.com/blog/entry/43615">345</a>, <a href="https://codeforces.com/blog/entry/47769">376</a>, <a href="https://codeforces.com/blog/entry/50613">401</a>, <a href="https://codeforces.com/blog/entry/54350">433</a>, <a href="https://codeforces.com/blog/entry/55201">441</a>, <a href="https://codeforces.com/blog/entry/57981">466</a>, <a href="https://codeforces.com/blog/entry/58229">469</a>, <a href="https://codeforces.com/blog/entry/61638">507</a>, <a href="https://codeforces.com/blog/entry/62440">516</a>, <a href="https://codeforces.com/blog/entry/65433">541</a>, <a href="https://codeforces.com/blog/entry/65768">545</a>, <a href="https://codeforces.com/blog/entry/67703">567</a>, <a href="https://codeforces.com/blog/entry/69538">583</a>, <a href="https://codeforces.com/blog/entry/70680">594</a>, <a href="https://codeforces.com/blog/entry/74106">622</a>, <a href="https://codeforces.com/blog/entry/52457">626</a>, <a href="https://codeforces.com/blog/entry/80214">657</a>, <a href="https://codeforces.com/blog/entry/84198">680</a>, <a href="https://codeforces.com/blog/entry/87791">704</a>).</p><p>Open Olympiad consists of the most interesting and hard problems that are proposed by a wide community of authors, so we decided to conduct a Codeforces regular round based on it, which will happen on <a class="contest-time" contestid="1500" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=13&amp;month=3&amp;year=2021&amp;hour=12&amp;min=5&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/13/2021 12:05 (Moscow time)</span></a> and will be based on <strong>both</strong> days of the Olympiad. Each division will have 6 problems and 2 and a half hours to solve them.</p><p><strong>We kindly ask all the community members that are going to participate in the competition to show sportsmanship by not trying to cheat in any manner, in particular, by trying to figure out problem statements from the onsite participants. If you end up knowing some of the problems of Moscow Open Olympiad (by participating in it, from some of the onsite contestants or in any other way), please do not participate in the round. We also ask onsite contestants to not discuss problems in public. Failure to comply with any of the rules above may result in a disqualification.</strong></p><p>Problems of this competition were prepared by <a class="rated-user user-red" href="/profile/Akulyat" title="Grandmaster Akulyat">Akulyat</a>, <a class="rated-user user-orange" href="/profile/KiKoS" title="Master KiKoS">KiKoS</a>, <a class="rated-user user-blue" href="/profile/wrg0ababd" title="Expert wrg0ababd">wrg0ababd</a>, <a class="rated-user user-red" href="/profile/Nebuchadnezzar" title="International Grandmaster Nebuchadnezzar">Nebuchadnezzar</a>, <a class="rated-user user-orange" href="/profile/biection" title="Master biection">biection</a>, <a class="rated-user user-orange" href="/profile/alexX512" title="Master alexX512">alexX512</a> <a class="rated-user user-red" href="/profile/isaf27" title="International Grandmaster isaf27">isaf27</a>, <a class="rated-user user-red" href="/profile/ismagilov.code" title="Grandmaster ismagilov.code">ismagilov.code</a>, <a class="rated-user user-orange" href="/profile/DebNatkh" title="Master DebNatkh">DebNatkh</a>, <a class="rated-user user-orange" href="/profile/Siberian" title="International Master Siberian">Siberian</a>, <a class="rated-user user-orange" href="/profile/NiceClock" title="Master NiceClock">NiceClock</a> guided by <a class="rated-user user-red" href="/profile/cdkrot" title="International Grandmaster cdkrot">cdkrot</a>, <a class="rated-user user-red" href="/profile/vintage_Vlad_Makeev" title="International Grandmaster vintage_Vlad_Makeev">vintage_Vlad_Makeev</a>, <a class="rated-user user-red" href="/profile/GlebsHP" title="International Grandmaster GlebsHP">GlebsHP</a>, <a class="rated-user user-red" href="/profile/Zlobober" title="International Grandmaster Zlobober">Zlobober</a>, <a class="rated-user user-red" href="/profile/meshanya" title="Grandmaster meshanya">meshanya</a>, <a class="rated-user user-red" href="/profile/ch_egor" title="International Grandmaster ch_egor">ch_egor</a>, <a class="rated-user user-orange" href="/profile/grphil" title="Master grphil">grphil</a>, <a class="rated-user user-red" href="/profile/voidmax" title="Grandmaster voidmax">voidmax</a>, <a class="rated-user user-legendary" href="/profile/Endagorion" title="Legendary Grandmaster Endagorion"><span class="legendary-user-first-letter">E</span>ndagorion</a> and Helen Andreeva.</p><p>Thanks to <a class="rated-user user-orange" href="/profile/adedalic" title="International Master adedalic">adedalic</a> and <a class="rated-user user-red" href="/profile/KAN" title="International Grandmaster KAN">KAN</a> for the round coordination, statement translation and preparation of problems for the second division, and also thanks for <a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> for systems Codeforces and Polygon, which was used to prepare problems of this olympiad.</p><p>Also thanks to <a class="rated-user user-blue" href="/profile/4qqqq" title="Expert 4qqqq">4qqqq</a> and <a class="rated-user user-red" href="/profile/Aleks5d" title="Grandmaster Aleks5d">Aleks5d</a> for providing an additional problems that helped to create (I hope) a balanced problem set for the round, and <a class="rated-user user-legendary" href="/profile/Um_nik" title="Legendary Grandmaster Um_nik"><span class="legendary-user-first-letter">U</span>m_nik</a> for testing!</p><p>Good luck everybody!</p><p>Due to the official competition source codes of other participants will not be available for an hour after the end of the round. </p><p><strong>UPD1:</strong> </p><p><strong>Please do not discuss problems publicly until 12:30 UTC.</strong></p><p>The scoring distribution for both divisions is not standard: </p> <ul>   <li>div1: 750 — 750 — 1500 — 2000 — 2500 — 3000</li>   <li>div2: 500 — 1000 — 1750 — 1750 — 2500 — 3000</li> </ul><p><strong>UPD2:</strong> <a href="https://codeforces.com/blog/entry/88591">Editorial</a></p><p><strong>UPD3</strong>: Winners!</p><p>Div. 1:</p> <ol>   <li><a class="rated-user user-legendary" href="/profile/tourist" title="Legendary Grandmaster tourist"><span class="legendary-user-first-letter">t</span>ourist</a></li>   <li><a class="rated-user user-legendary" href="/profile/jiangly" title="Legendary Grandmaster jiangly"><span class="legendary-user-first-letter">j</span>iangly</a></li>   <li><a class="rated-user user-legendary" href="/profile/maroonrk" title="Legendary Grandmaster maroonrk"><span class="legendary-user-first-letter">m</span>aroonrk</a></li>   <li><a class="rated-user user-legendary" href="/profile/ecnerwala" title="Legendary Grandmaster ecnerwala"><span class="legendary-user-first-letter">e</span>cnerwala</a></li>   <li><a class="rated-user user-legendary" href="/profile/Miracle03" title="Legendary Grandmaster Miracle03"><span class="legendary-user-first-letter">M</span>iracle03</a></li> </ol><p>Div. 2:</p> <ol>   <li><a class="rated-user user-cyan" href="/profile/wudi2016" title="Specialist wudi2016">wudi2016</a></li>   <li><a class="rated-user user-blue" href="/profile/ShimaRin" title="Expert ShimaRin">ShimaRin</a></li>   <li><a class="rated-user user-green" href="/profile/fengqiyuka" title="Pupil fengqiyuka">fengqiyuka</a></li>   <li><a class="rated-user user-gray" href="/profile/gezlik" title="Newbie gezlik">gezlik</a></li>   <li><a class="rated-user user-gray" href="/profile/b___" title="Newbie b___">b___</a></li> </ol></div><p><a href="/blog/entry/88590">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1500" class="notice" style="text-decoration: none;">Codeforces Round #707 (Div. 1, based on Moscow Open Olympiad in Informatics)</a> </span>
                    </div>
                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1501" class="notice" style="text-decoration: none;">Codeforces Round #707 (Div. 2, based on Moscow Open Olympiad in Informatics)</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-42191-89136").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "42191",
                                        blogEntryId: "88590",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>


                    <div style="font-size: 1.1rem;line-height: 11px;">
                        <img style="vertical-align: middle;" src="//sta.codeforces.com/s/76577/images/blog/tags.png" title="Tags" alt="Tags"/>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=707" class="tag notice" style="text-decoration: none;">707</a>,
                </span>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=round" class="tag notice" style="text-decoration: none;">round</a>
                </span>
                    </div>

                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-89136"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:gray'>-661</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-89136"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/ch_egor"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                            alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/ch_egor">
                                        ch_egor
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/12/2021 00:04">8 days ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88590#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88590#comments">
                                        416
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-89136").click(function () {
                                $.post("/data/topic/vote", {topicId: 89136, _tta: Codeforces.tta(), topicRevisionId: 220314, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-89136").click(function () {
                                $.post("/data/topic/vote", {topicId: 89136, _tta: Codeforces.tta(), topicRevisionId: 220314, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="89043">
                    <div class="title">
                        <a href="/blog/entry/88497">            <p>Codeforces Round #706</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/Imakf" title="Master Imakf" class="rated-user user-orange">Imakf</a>,
                        <a href="/topic/89043/en7">history</a>,
                        <span class="format-humantime" title="Mar/09/2021 08:58">10 days ago</span>,

                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello, Codeforces!</p><p><a class="rated-user user-orange" href="/profile/Daniel_yuan" title="Master Daniel_yuan">Daniel_yuan</a>, <a class="rated-user user-orange" href="/profile/waaitg" title="Master waaitg">waaitg</a>, <a class="rated-user user-blue" href="/profile/smg23333" title="Expert smg23333">smg23333</a> and I are glad to invite you to <a href="/contest/1495" title="Codeforces Round 706 (Div. 1)">Codeforces Round #706 (Div. 1)</a> and <a href="/contest/1496" title="Codeforces Round 706 (Div. 2)">Codeforces Round #706 (Div. 2)</a>, which will take place on <a class="contest-time" contestid="1495" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=10&amp;month=3&amp;year=2021&amp;hour=15&amp;min=5&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/10/2021 15:05 (Moscow time)</span></a>. <strong>Note the unusual time of the round.</strong> In both divisions, you will be given <strong>6 problems</strong> and <strong>2 hours</strong> to solve them all.</p><p>We would like to thank:</p> <ul>   <li><a class="rated-user user-red" href="/profile/isaf27" title="International Grandmaster isaf27">isaf27</a> for the great coordination and discussion.</li>   <li><a class="rated-user user-legendary" href="/profile/KAN" title="Legendary Grandmaster KAN"><span class="legendary-user-first-letter">K</span>AN</a> for the great help with round preparation.</li>   <li><a class="rated-user user-violet" href="/profile/Wolam" title="Candidate Master Wolam">Wolam</a> for <s>all his rejected problems and</s> discussing the problems with us.</li>   <li><a class="rated-user user-orange" href="/profile/tiger0132" title="Master tiger0132">tiger0132</a>, <a class="rated-user user-red" href="/profile/EmptySoulist" title="Grandmaster EmptySoulist">EmptySoulist</a>, <a class="rated-user user-orange" href="/profile/CYJian" title="Master CYJian">CYJian</a>, <a class="rated-user user-cyan" href="/profile/parallelist" title="Specialist parallelist">parallelist</a>, <a class="rated-user user-cyan" href="/profile/Sunward_z" title="Specialist Sunward_z">Sunward_z</a> and <a class="rated-user user-orange" href="/profile/ODT" title="Master ODT">ODT</a> for testing the problems in advance and sharing their ideas.</li>   <li><a class="rated-user user-orange" href="/profile/dXqwq" title="Master dXqwq">dXqwq</a>, <a class="rated-user user-orange" href="/profile/LTb" title="Master LTb">LTb</a>, <a class="rated-user user-blue" href="/profile/rui_er" title="Expert rui_er">rui_er</a>, <a class="rated-user user-blue" href="/profile/CSP_Sept" title="Expert CSP_Sept">CSP_Sept</a>, <a class="rated-user user-orange" href="/profile/Qiuly.qwq" title="Master Qiuly.qwq">Qiuly.qwq</a>, <a class="rated-user user-blue" href="/profile/mac" title="Expert mac">mac</a>, <a class="rated-user user-orange" href="/profile/dapingguo8" title="Master dapingguo8">dapingguo8</a>, <a class="rated-user user-blue" href="/profile/mazihang2022" title="Expert mazihang2022">mazihang2022</a>, <a class="rated-user user-red" href="/profile/Ashishgup" title="Grandmaster Ashishgup">Ashishgup</a>, <a class="rated-user user-orange" href="/profile/aajisaka" title="Master aajisaka">aajisaka</a>, <a class="rated-user user-gray" href="/profile/i_wyxkk_ak" title="Newbie i_wyxkk_ak">i_wyxkk_ak</a>, <a class="rated-user user-red" href="/profile/marX" title="International Grandmaster marX">marX</a>, <a class="rated-user user-orange" href="/profile/Mitsukasa_Ayase" title="Master Mitsukasa_Ayase">Mitsukasa_Ayase</a>, <a class="rated-user user-blue" href="/profile/Oak_limy" title="Expert Oak_limy">Oak_limy</a>, <a class="rated-user user-gray" href="/profile/sidiangongyuan" title="Newbie sidiangongyuan">sidiangongyuan</a>, <a class="rated-user user-red" href="/profile/zscoder" title="International Grandmaster zscoder">zscoder</a>, <a class="rated-user user-violet" href="/profile/tomato_sans" title="Candidate Master tomato_sans">tomato_sans</a>, <a class="rated-user user-violet" href="/profile/lgswdn" title="Candidate Master lgswdn">lgswdn</a>, <a class="rated-user user-red" href="/profile/tianbu" title="Grandmaster tianbu">tianbu</a>, <a class="rated-user user-orange" href="/profile/orzdevinwang" title="International Master orzdevinwang">orzdevinwang</a>, <a class="rated-user user-cyan" href="/profile/happydef" title="Specialist happydef">happydef</a>, <a class="rated-user user-orange" href="/profile/Kewth" title="International Master Kewth">Kewth</a>, <a class="rated-user user-orange" href="/profile/Widowmaker" title="Master Widowmaker">Widowmaker</a>, <a class="rated-user user-orange" href="/profile/star_xingchen_c" title="Master star_xingchen_c">star_xingchen_c</a>, <a class="rated-user user-red" href="/profile/3.141592653" title="Grandmaster 3.141592653">3.141592653</a>, <a class="rated-user user-blue" href="/profile/Aaeria" title="Expert Aaeria">Aaeria</a> and <a class="rated-user user-violet" href="/profile/4c5948" title="Candidate Master 4c5948">4c5948</a> for testing the round and their invaluable feedbacks.</li>   <li><a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> for amazing platform Codeforces and Polygon.</li> </ul><p>Score distribution will be announced before the round.</p><p>Hope you all gain positive ratings $$$\Delta$$$ in this round!</p><p><strong>UPD1</strong>: Score distribution is</p><p><strong>Div. 2</strong>: $$$500-1000-1500-2000-2500-3000$$$</p><p><strong>Div. 1</strong>: $$$500-1000-1500-2000-2500-3250$$$</p><p><strong>UPD2</strong>: <a href="https://codeforces.com/blog/entry/88533">Editorial</a></p><p><strong>UPD3</strong>: Congratulations to the winners:</p><p><strong>Div 1:</strong></p> <ol>   <li><a class="rated-user user-legendary" href="/profile/Radewoosh" title="Legendary Grandmaster Radewoosh"><span class="legendary-user-first-letter">R</span>adewoosh</a></li>   <li><a class="rated-user user-legendary" href="/profile/maroonrk" title="Legendary Grandmaster maroonrk"><span class="legendary-user-first-letter">m</span>aroonrk</a></li>   <li><a class="rated-user user-legendary" href="/profile/tourist" title="Legendary Grandmaster tourist"><span class="legendary-user-first-letter">t</span>ourist</a></li>   <li><a class="rated-user user-legendary" href="/profile/Um_nik" title="Legendary Grandmaster Um_nik"><span class="legendary-user-first-letter">U</span>m_nik</a></li>   <li><a class="rated-user user-red" href="/profile/Egor" title="International Grandmaster Egor">Egor</a></li> </ol><p><strong>Div 2:</strong></p> <ol>   <li><a class="rated-user user-black" href="/profile/shikaichengwoerzi" title="Unrated, shikaichengwoerzi">shikaichengwoerzi</a></li>   <li><a class="rated-user user-blue" href="/profile/grey" title="Expert grey">grey</a></li>   <li><a class="rated-user user-blue" href="/profile/2005lz" title="Expert 2005lz">2005lz</a></li>   <li><a class="rated-user user-blue" href="/profile/sh_mug" title="Expert sh_mug">sh_mug</a></li>   <li><a class="rated-user user-blue" href="/profile/not_tehlka" title="Expert not_tehlka">not_tehlka</a></li> </ol></div><p><a href="/blog/entry/88497">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1495" class="notice" style="text-decoration: none;">Codeforces Round #706 (Div. 1)</a> </span>
                    </div>
                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1496" class="notice" style="text-decoration: none;">Codeforces Round #706 (Div. 2)</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-106548-89043").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "106548",
                                        blogEntryId: "88497",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>



                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-89043"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+561</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-89043"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/Imakf"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                          alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/Imakf">
                                        Imakf
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/09/2021 08:58">10 days ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88497#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88497#comments">
                                        333
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-89043").click(function () {
                                $.post("/data/topic/vote", {topicId: 89043, _tta: Codeforces.tta(), topicRevisionId: 220051, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-89043").click(function () {
                                $.post("/data/topic/vote", {topicId: 89043, _tta: Codeforces.tta(), topicRevisionId: 220051, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="88939">
                    <div class="title">
                        <a href="/blog/entry/88393">            <p>Kotlin Heroes 6 Announcement</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/BledDest" title="International Grandmaster BledDest" class="rated-user user-red">BledDest</a>,
                        <span class="format-humantime" title="Mar/05/2021 11:56">2 weeks ago</span>,

                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p><img src="/predownloaded/6b/62/6b62332b37519ade4a4d42cb266937c422b64557.png" style="width: 500.0px;float: right;margin: 0 1.0em 1.0em 1.0em;max-width: 100.0%;max-height: 100.0%;" /></p><p>Hello, Codeforces!</p><p>First and foremost, we would like to say a massive thank you to everyone who entered and submitted their answers to the five Kotlin Heroes competitions which were held previously: <a href="https://codeforces.com/contests/1170">Episode 1</a>, <a href="https://codeforces.com/contests/1211">Episode 2</a>, <a href="https://codeforces.com/contests/1297">Episode 3</a>, <a href="https://codeforces.com/contests/1346">Episode 4</a>, and <a href="https://codeforces.com/contest/1431">Episode 5: ICPC Round</a>.</p><p>Ready to challenge yourself to do better? The <a href="/contest/1488" title="Kotlin Heroes: Episode 6">Kotlin Heroes: Episode 6</a> competition will be hosted on the Codeforces platform on <a class="contest-time" contestid="1488" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=9&amp;month=3&amp;year=2021&amp;hour=17&amp;min=35&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/09/2021 17:35 (Moscow time)</span></a>. The contest will last 2 hours 30 minutes and will feature a set of problems from simple ones, designed to be solvable by anyone, to hard ones, to make it interesting for seasoned competitive programmers. </p><p><strong>Prizes:</strong></p><p><strong>Top three winners will get prizes of $512, $256, and $128 respectively, top 50 will win a Kotlin Heroes t-shirt and an exclusive Kotlin sticker, competitors solving at least one problem will enter into a draw for one of 50 Kotlin Heroes t-shirts.</strong></p><p>Registration is already open and available via <a href="https://codeforces.com/contests/1488,1489">the link</a>. It will be available until the end of the round.</p><p>The round will again be held in accordance with a set of slightly modified ICPC rules:</p> <ul>   <li>The round is unrated.</li>   <li>The contest will have 9 problems of various levels of complexity.</li>   <li>You are only allowed to use Kotlin to solve these problems.</li>   <li>Participants are ranked according to the number of correctly solved problems. Ties are resolved based on the lowest total penalty time for all problems, which is computed as follows. For each solved problem, a penalty is set to the submission time of that problem (the time since the start of the contest). An extra penalty of 10 minutes is added for each failed submission on solved problems (i. e., if you never solve the problem, you will not be penalized for trying that problem). If two participants solved the same number of problems and scored the same penalty, then those of them who had previously made the last successful submission will be given an advantage in the distribution of prizes and gifts.</li> </ul> <p style="margin: 2.5em;"> </p><center> <a href="/contests/1488,1489" style="font-size: 18.0px;background-color: rgb(75,124,186);border: 1.0px solid rgb(26,35,126);padding: 8.0px 18.0px;color: white;font-weight: bold;text-decoration: none;">REGISTER →</a> </center> <p>If you are still new to Kotlin we have prepared <a href="https://kotlinlang.org/docs/tutorials/competitive-programming.html">a tutorial on competitive programming in Kotlin</a> and <a href="/contest/1489" title="Kotlin Heroes: Practice 6">Kotlin Heroes: Practice 6</a>, where you can try to solve a few simple problems in Kotlin. The practice round is available by <a href="https://codeforces.com/contests/1489">the link</a>.</p><p>We wish you luck and hope you enjoy Kotlin.</p></div><p><a href="/blog/entry/88393">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1488" class="notice" style="text-decoration: none;">Kotlin Heroes: Episode 6</a> </span>
                    </div>
                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1489" class="notice" style="text-decoration: none;">Kotlin Heroes: Practice 6</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-53375-88939").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "53375",
                                        blogEntryId: "88393",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>



                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-88939"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+157</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-88939"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/BledDest"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                             alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/BledDest">
                                        BledDest
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/05/2021 11:56">2 weeks ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88393#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88393#comments">
                                        37
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-88939").click(function () {
                                $.post("/data/topic/vote", {topicId: 88939, _tta: Codeforces.tta(), topicRevisionId: 219629, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-88939").click(function () {
                                $.post("/data/topic/vote", {topicId: 88939, _tta: Codeforces.tta(), topicRevisionId: 219629, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="89020">
                    <div class="title">
                        <a href="/blog/entry/88474">            <p>Video about prefix sums, difference array and the power of half-closed intervals</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/peltorator" title="Grandmaster peltorator" class="rated-user user-red">peltorator</a>,
                        <a href="/topic/89020/en1">history</a>,
                        <span class="format-humantime" title="Mar/08/2021 14:08">11 days ago</span>,
                        translation,
                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hi!</p><p>I continue to make videos on algorithms. This time the topic is more basic. In this video, I talk about prefix sums and how they can help you to find sum on segments. You can also learn from this video how to easily generalize prefix sums for 2D, 3D, 4D, etc. cases. In addition, we'll also talk about a simple concept named difference array, which can easily help in some sorts of situations where it seems like you need some complex data structures. And in the end, we'll learn how to add constants, arithmetic progressions, and even quadratic functions to a segment of an array.</p><p><a href="https://youtu.be/5iW84xlL0j0">https://youtu.be/5iW84xlL0j0</a></p><p>The video is in Russian but English subtitles are available. I'd be glad if you watch the video and leave a comment below with your impressions, thoughts, and ideas for future videos. You may also want to text me on telegram: <a href="https://t.me/peltorator">https://t.me/peltorator</a> if you didn't understand something or you have any questions. I'll be glad to answer!</p><p>I'm sorry you need to watch it with subtitles but I'm gonna make an English channel soon. So stay tuned!</p><p><img alt=" " src="/predownloaded/35/2c/352c7a48774529b0484dd14b352e46335529e6cb.png" style="max-width: 100.0%;max-height: 100.0%;" /></p><p>If you didn't see it already, I also have a video on disjoint sparse table: <a href="https://youtu.be/NbAtm1j5gVA">https://youtu.be/NbAtm1j5gVA</a>.</p><p>Codeforces group with a contest: <a href="https://codeforces.com/group/1rv4rhCsHp/contests">https://codeforces.com/group/1rv4rhCsHp/contests</a></p><p>My realizations:</p><p>1D prefix sums: <a href="https://pastebin.com/MjxG7y43">https://pastebin.com/MjxG7y43</a></p><p>1D prefix sums with structures: <a href="https://pastebin.com/062t332c">https://pastebin.com/062t332c</a></p><p>2 methods for finding 2D prefix sums: <a href="https://pastebin.com/a09xCDGw">https://pastebin.com/a09xCDGw</a> <a href="https://pastebin.com/Yezy0Lkb">https://pastebin.com/Yezy0Lkb</a></p><p>1D difference array: <a href="https://pastebin.com/fXpwTRiK">https://pastebin.com/fXpwTRiK</a></p><p>1D difference array with structures: <a href="https://pastebin.com/fbmveX6Q">https://pastebin.com/fbmveX6Q</a></p></div><p><a href="/blog/entry/88474">Read more »</a></p>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-73105-89020").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "73105",
                                        blogEntryId: "88474",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>


                    <div style="font-size: 1.1rem;line-height: 11px;">
                        <img style="vertical-align: middle;" src="//sta.codeforces.com/s/76577/images/blog/tags.png" title="Tags" alt="Tags"/>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=youtube" class="tag notice" style="text-decoration: none;">youtube</a>
                </span>
                    </div>

                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-89020"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+349</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-89020"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/peltorator"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                               alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/peltorator">
                                        peltorator
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/08/2021 14:08">11 days ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88474#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88474#comments">
                                        25
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-89020").click(function () {
                                $.post("/data/topic/vote", {topicId: 89020, _tta: Codeforces.tta(), topicRevisionId: 219868, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-89020").click(function () {
                                $.post("/data/topic/vote", {topicId: 89020, _tta: Codeforces.tta(), topicRevisionId: 219868, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="88892">
                    <div class="title">
                        <a href="/blog/entry/88346">            <p>Codeforces Round #705 (Div. 2)</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/AlFlen" title="Master AlFlen" class="rated-user user-orange">AlFlen</a>,
                        <span class="format-humantime" title="Mar/03/2021 17:15">2 weeks ago</span>,
                        translation,
                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p style="text-align: right;"><i>The future is bulletproof<br /> The aftermath is secondary<br /> It's time to do it now and do it loud!</i></p><p>Hello, Codeforces!</p><p><a class="rated-user user-orange" href="/profile/74TrAkToR" title="Master 74TrAkToR">74TrAkToR</a> and I are glad to invite you to our <a href="/contest/1493" title="Codeforces Round 705 (Div. 2)">Codeforces Round #705 (Div. 2)</a>, which will be held at <a class="contest-time" contestid="1493" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=6&amp;month=3&amp;year=2021&amp;hour=17&amp;min=5&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/06/2021 17:05 (Moscow time)</span></a>. Notice the unusual time of the round. <b>The round will be rated for all the participants with rating strictly less than 2100.</b></p><p>We have already held a round and we have worked on errors:</p> <ul> <li>the statements will be short and clear</li> <li>we tried to make pretests stronger</li> <li>the editorial will be published shortly after the round ends</li> </ul><p>We would like to thank everyone who helped us a lot with round preparation.</p> <ul style="list-style-type: square;"> <li>Our coordinator <a class="rated-user user-legendary" href="/profile/KAN" title="Legendary Grandmaster KAN"><span class="legendary-user-first-letter">K</span>AN</a> for brilliant coordination of the round.</li> <li>Our testers <a class="rated-user user-red" href="/profile/Tlatoani" title="International Grandmaster Tlatoani">Tlatoani</a>, <a class="rated-user user-red" href="/profile/sava-cska" title="Grandmaster sava-cska">sava-cska</a>, <a class="rated-user user-orange" href="/profile/DIvanCode" title="Master DIvanCode">DIvanCode</a>, <a class="rated-user user-orange" href="/profile/BLIZZARD" title="International Master BLIZZARD">BLIZZARD</a>, <a class="rated-user user-orange" href="/profile/Mlxa" title="Master Mlxa">Mlxa</a>, <a class="rated-user user-blue" href="/profile/gerasikov.fml31" title="Expert gerasikov.fml31">gerasikov.fml31</a>, <a class="rated-user user-violet" href="/profile/voventa" title="Candidate Master voventa">voventa</a>, <a class="rated-user user-orange" href="/profile/hg333" title="Master hg333">hg333</a>, <a class="rated-user user-orange" href="/profile/golions" title="Master golions">golions</a>, <a class="rated-user user-blue" href="/profile/kassutta" title="Expert kassutta">kassutta</a> for great testing and helpful notes.</li> <li><a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> for amazing Codeforces and Polygon platforms.</li> </ul><p>You will be given <b>6 problems</b>. You will have <b>2 hours 15 minutes</b> to solve them.</p><p><b>UPD</b>: Score distribution $$$750-1250-1750-2250-2750-3250$$$.</p><p><b>UPD2</b>: <a href="https://codeforces.com/blog/entry/88422">Editorial</a></p><p><b>UPD3</b>: Congratulations to the winners!</p><p>Div. 2:</p> <ol>   <li><p><a class="rated-user user-blue" href="/profile/sawa855" title="Expert sawa855">sawa855</a></p></li>   <li><p><a class="rated-user user-violet" href="/profile/buihoatpt2k9" title="Candidate Master buihoatpt2k9">buihoatpt2k9</a></p></li>   <li><p><a class="rated-user user-violet" href="/profile/Totiniii" title="Candidate Master Totiniii">Totiniii</a></p></li>   <li><p><a class="rated-user user-cyan" href="/profile/rainboy" title="Specialist rainboy">rainboy</a></p></li>   <li><p><a class="rated-user user-blue" href="/profile/scli_kws" title="Expert scli_kws">scli_kws</a></p></li> </ol><p>Div. 1 + Div. 2:</p> <ol>   <li><p><a class="rated-user user-legendary" href="/profile/neal" title="Legendary Grandmaster neal"><span class="legendary-user-first-letter">n</span>eal</a></p></li>   <li><p><a class="rated-user user-red" href="/profile/BigBag" title="Grandmaster BigBag">BigBag</a></p></li>   <li><p><a class="rated-user user-red" href="/profile/fastmath" title="International Grandmaster fastmath">fastmath</a></p></li>   <li><p><a class="rated-user user-red" href="/profile/Heltion" title="International Grandmaster Heltion">Heltion</a></p></li>   <li><p><a class="rated-user user-red" href="/profile/tute7627" title="Grandmaster tute7627">tute7627</a></p></li> </ol><p>We wish everyone good luck!</p></div><p><a href="/blog/entry/88346">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1493" class="notice" style="text-decoration: none;">Codeforces Round #705 (Div. 2)</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-110858-88892").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "110858",
                                        blogEntryId: "88346",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>



                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-88892"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+676</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-88892"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/AlFlen"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                           alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/AlFlen">
                                        AlFlen
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/03/2021 17:15">2 weeks ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88346#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88346#comments">
                                        253
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-88892").click(function () {
                                $.post("/data/topic/vote", {topicId: 88892, _tta: Codeforces.tta(), topicRevisionId: 219745, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-88892").click(function () {
                                $.post("/data/topic/vote", {topicId: 88892, _tta: Codeforces.tta(), topicRevisionId: 219745, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="88830">
                    <div class="title">
                        <a href="/blog/entry/88284">            <p>Educational Codeforces Round 105 [Rated for Div. 2]</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/awoo" title="Grandmaster awoo" class="rated-user user-red">awoo</a>,
                        <a href="/topic/88830/en5">history</a>,
                        <span class="format-humantime" title="Mar/01/2021 16:11">3 weeks ago</span>,
                        translation,
                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello Codeforces!</p><p>On <a class="contest-time" contestid="1494" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=2&amp;month=3&amp;year=2021&amp;hour=17&amp;min=45&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Mar/02/2021 17:45 (Moscow time)</span></a> <a href="/contest/1494" title="Educational Codeforces Round 105 (Rated for Div. 2)">Educational Codeforces Round 105 (Rated for Div. 2)</a> will start.</p><p>Series of Educational Rounds continue being held as <a href="https://harbour.space/">Harbour.Space University</a> initiative! You can read the details about the cooperation between <a href="https://harbour.space/">Harbour.Space University</a> and Codeforces in the <a href="//codeforces.com/blog/entry/51208">blog post</a>.</p><p>This round will be <strong>rated for the participants with rating lower than 2100</strong>. It will be held on extended ICPC rules. The penalty for each incorrect submission until the submission with a full solution is 10 minutes. After the end of the contest you will have 12 hours to hack any solution you want. You will have access to copy any solution and test it locally.</p><p>You will be given <strong>6 or 7 problems</strong> and <strong>2 hours</strong> to solve them.</p><p>The problems were invented and prepared by Roman <a class="rated-user user-orange" href="/profile/Roms" title="Master Roms">Roms</a> Glazov, Adilbek <a class="rated-user user-orange" href="/profile/adedalic" title="International Master adedalic">adedalic</a> Dalabaev, Vladimir <a class="rated-user user-orange" href="/profile/vovuh" title="Master vovuh">vovuh</a> Petrov, Ivan <a class="rated-user user-red" href="/profile/BledDest" title="International Grandmaster BledDest">BledDest</a> Androsov, Maksim <a class="rated-user user-violet" href="/profile/Neon" title="Candidate Master Neon">Neon</a> Mescheryakov and me. Also huge thanks to Mike <a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> Mirzayanov for great systems Polygon and Codeforces.</p><p>Good luck to all the participants!</p><p>Our friends at Harbour.Space also have a message for you:</p><p><img alt="Codeforces and Harbour.Space" src="/predownloaded/55/b2/55b26b00db279fd08b86717118ba4c0e4a4ffc1f.png" style="margin: 10.0px;max-width: 100.0%;max-height: 100.0%;" /></p><p><em>Amazing news once again, Codeforces!</em></p><p><em>We are especially glad to have a chance to share our <strong>scholarship opportunities</strong> more often!</em> </p><p><em>This time we have partnered with OneRagtime again to open the door for an exciting career in technology for the most talented people in our network.</em> </p><p><em>In partnership with OneRagtime, we are offering a full scholarship to study a Master’s in Computer Science at Harbour.Space while working as a <a href="https://harbour.space/scholarships/computer-science-apprenticeship-oneragtime?utm_source=codeforces&amp;utm_medium=referral&amp;utm_campaign=oneragtime-fullstack&amp;utm_content=">Full Stack Developer</a> at OneRagtime!</em></p><p><em>Scholarship Highlights:</em></p><p>➡ <em>Work in Europe’s most exciting tech cities</em></p><p>➡ <em>Scholarship value of up to €31,500</em></p><p>➡ <em>Competitive compensation for the internship at OneRagtime (€800 / month)</em></p><p>➡ <em>Opportunity to join OneRagtime full-time after graduation</em></p><p><em>Some of the advantages of working at OneRagtime:</em></p> <ul>   <li><em>International team</em></li>   <li><em>Fast-paced workplace</em></li>   <li><em>Be a part of the OneRagtime adventure!</em></li>   <li><em>Be fully immersed in the European tech ecosystem</em></li>   <li><em>Thrive within a Venture Capital that does things a little differently</em></li>   <li><em>Work in Europe’s most exciting tech cities</em></li> </ul><p><img alt="Codeforces and Harbour.Space" src="/predownloaded/ce/44/ce44951edb73aa0c109a60106eab6b0ec2170623.png" style="margin: 10.0px;max-width: 100.0%;max-height: 100.0%;" /></p><p><em>We have previously partnered with other companies like OneRagtime, Hansgrohe, Coherra, and Remy Robotics to empower young talents around the world and help them boost their tech career. We’ve already filled a few of the positions with OneRagtime including:</em></p> <ul>   <li><em>Full Stack Developer at <strong>OneRagtime</strong> awarded to Alejandro Martinez from Mexico</em></li>   <li><em>UI/UX designer at <strong>OneRagtime</strong> awarded to <a href="https://youtu.be/9s7vrYXUNwo">Davit Petriashvili</a> from Georgia</em></li> </ul><p><em>We are always happy to see Codeforces members join the Harbour.Space family. Apply now to get a chance to learn from the best in the field and kickstart your career!</em></p><p><em>Keep in touch and follow us on <a href="https://www.linkedin.com/school/harbour-space/">LinkedIn</a> for more scholarship opportunities. And follow us on <a href="https://www.instagram.com/harbour.space/">Instagram</a> to evidence student life, events, and success stories from our apprenticeship program students.</em></p><p><em>Good luck on your round, and see you next time!</em> </p><p><em>Harbour.Space University</em></p><p>Congratulations to the winners: <table>  <tbody>  <tr>  <th>Rank</th>  <th>Competitor</th>  <th>Problems Solved</th>  <th>Penalty</th>  </tr>  <tr>  <td>1</td>  <td><a class="rated-user user-red" href="/profile/antontrygubO_o" title="International Grandmaster antontrygubO_o">antontrygubO_o</a></td>  <td>6</td>  <td>251</td>  </tr>  <tr>  <td>1</td>  <td><a class="rated-user user-red" href="/profile/Pyqe" title="Grandmaster Pyqe">Pyqe</a></td>  <td>6</td>  <td>251</td>  </tr>  <tr>  <td>3</td>  <td><a class="rated-user user-legendary" href="/profile/kefaa2" title="Legendary Grandmaster kefaa2"><span class="legendary-user-first-letter">k</span>efaa2</a></td>  <td>6</td>  <td>260</td>  </tr>  <tr>  <td>4</td>  <td><a class="rated-user user-red" href="/profile/tute7627" title="Grandmaster tute7627">tute7627</a></td>  <td>6</td>  <td>272</td>  </tr>  <tr>  <td>5</td>  <td><a class="rated-user user-legendary" href="/profile/Um_nik" title="Legendary Grandmaster Um_nik"><span class="legendary-user-first-letter">U</span>m_nik</a></td>  <td>6</td>  <td>288</td>  </tr>  </tbody> </table></p><p>Congratulations to the best hackers: <table>  <tbody>  <tr>  <th>Rank</th>  <th>Competitor</th>  <th>Hack Count</th>  </tr>  <tr>  <td>1</td>  <td><a class="rated-user user-red" href="/profile/noimi" title="International Grandmaster noimi">noimi</a></td>  <td><b><font color="green">11</font></b></td>  </tr>  <tr>  <td>2</td>  <td><a class="rated-user user-legendary" href="/profile/neal" title="Legendary Grandmaster neal"><span class="legendary-user-first-letter">n</span>eal</a></td>  <td><b><font color="green">7</font></b></td>  </tr>  <tr>  <td>3</td>  <td><a class="rated-user user-red" href="/profile/Origenes" title="Grandmaster Origenes">Origenes</a></td>  <td><b><font color="green">6</font></b></td>  </tr>  <tr>  <td>4</td>  <td><a class="rated-user user-blue" href="/profile/Kregor" title="Expert Kregor">Kregor</a></td>  <td><b><font color="green">5</font></b><b>:<font color="red">-2</font></b></td>  </tr>  <tr>  <td>5</td>  <td><a class="rated-user user-gray" href="/profile/chilliagon" title="Newbie chilliagon">chilliagon</a></td>  <td><b><font color="green">5</font></b><b>:<font color="red">-4</font></b></td>  </tr>  </tbody> </table> 94 successful hacks and 293 unsuccessful hacks were made in total!</p><p>And finally people who were the first to solve each problem: <table>  <tbody>  <tr>  <th>Problem</th>  <th>Competitor</th>  <th>Penalty</th>  </tr>  <tr>  <td>A</td>  <td><a class="rated-user user-red" href="/profile/noimi" title="International Grandmaster noimi">noimi</a></td>  <td>0:01</td>  </tr>  <tr>  <td>B</td>  <td><a class="rated-user user-red" href="/profile/noimi" title="International Grandmaster noimi">noimi</a></td>  <td>0:04</td>  </tr>  <tr>  <td>C</td>  <td><a class="rated-user user-orange" href="/profile/wygzgyw" title="Master wygzgyw">wygzgyw</a></td>  <td>0:15</td>  </tr>  <tr>  <td>D</td>  <td><a class="rated-user user-orange" href="/profile/conan1412yang99" title="International Master conan1412yang99">conan1412yang99</a></td>  <td>0:16</td>  </tr>  <tr>  <td>E</td>  <td><a class="rated-user user-orange" href="/profile/thenymphsofdelphi" title="International Master thenymphsofdelphi">thenymphsofdelphi</a></td>  <td>0:15</td>  </tr>  <tr>  <td>F</td>  <td><a class="rated-user user-cyan" href="/profile/rainboy" title="Specialist rainboy">rainboy</a></td>  <td>0:35</td>  </tr>  </tbody> </table></p><p><strong>UPD:</strong> <a href="https://codeforces.com/blog/entry/88344">Editorial is out</a></p></div><p><a href="/blog/entry/88284">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1494" class="notice" style="text-decoration: none;">Educational Codeforces Round 105 (Rated for Div. 2)</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-42750-88830").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "42750",
                                        blogEntryId: "88284",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>



                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-88830"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+124</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-88830"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/awoo"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                         alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/awoo">
                                        awoo
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Mar/01/2021 16:11">3 weeks ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88284#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88284#comments">
                                        305
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-88830").click(function () {
                                $.post("/data/topic/vote", {topicId: 88830, _tta: Codeforces.tta(), topicRevisionId: 219511, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-88830").click(function () {
                                $.post("/data/topic/vote", {topicId: 88830, _tta: Codeforces.tta(), topicRevisionId: 219511, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>
            <div style="margin-bottom:2em;">


                <div class="topic" topicId="88679">
                    <div class="title">
                        <a href="/blog/entry/88134">            <p>Codeforces Global Round 13</p>
                        </a>
                    </div>

                    <div class="info" style="position:relative;">
                        By&nbsp;<a href="/profile/star_xingchen_c" title="Master star_xingchen_c" class="rated-user user-orange">star_xingchen_c</a>,
                        <span class="format-humantime" title="Feb/23/2021 16:46">3 weeks ago</span>,

                        <img style="position: relative;top: 5px;" src="//sta.codeforces.com/s/76577/images/flags/24/gb.png" alt="In English" title="In English"/>



                        <span style="position:absolute;right:0;top:0.05em;margin-right:1em;display:inline;font-size:0.75em;">
            <div style="margin-top:0.25em;">
            </div>
        </span>
                    </div>

                    <div class="content">
                        <div class="ttypography"><p>Hello Codeforces!</p><p>On <a class="contest-time" contestid="1491" href="https://www.timeanddate.com/worldclock/fixedtime.html?day=28&amp;month=2&amp;year=2021&amp;hour=16&amp;min=35&amp;sec=0&amp;p1=166"><span class="format-time-with-dow">Feb/28/2021 16:35 (Moscow time)</span></a> we will host <a href="/contest/1491" title="Codeforces Global Round 13">Codeforces Global Round 13</a>. <strong><img src="/predownloaded/2e/e2/2ee2f0f95f05aae6ca6f47812dae2dc2aabf277b.png" style="width: 200.0px;float: right;margin: 0 1.0em 1.0em 1.0em;max-width: 100.0%;max-height: 100.0%;" /></strong></p><p>It is the first round of a 2021 series of <a href="https://codeforces.com/blog/entry/65002">Codeforces Global Rounds</a>. The rounds are open and rated for everybody.</p><p>The prizes for this round:</p> <ul>   <li>30 best participants get a t-shirt.</li>   <li>20 t-shirts are randomly distributed among those with ranks between 31 and 500, inclusive.</li> </ul><p>The prizes for the 6-round series in 2021:</p> <ul>   <li>In each round top-100 participants get points according to the <a href="https://pastebin.com/QT5sXEaT">table</a>.</li>   <li>The final result for each participant is equal to the sum of points he gets in the four rounds he placed the highest.</li>   <li>The best 20 participants over all series get sweatshirts and place certificates.</li> </ul><p>Thanks to XTX, which in 2021 supported the global rounds initiative!</p><p>The problems were written and prepared by <a class="rated-user user-red" href="/profile/3.141592653" title="Grandmaster 3.141592653">3.141592653</a>, <a class="rated-user user-orange" href="/profile/Widowmaker" title="Master Widowmaker">Widowmaker</a>, <a class="rated-user user-orange" href="/profile/Ynoi" title="Master Ynoi">Ynoi</a>, <a class="rated-user user-red" href="/profile/errorgorn" title="Grandmaster errorgorn">errorgorn</a>, <a class="rated-user user-orange" href="/profile/oolimry" title="Master oolimry">oolimry</a>, <a class="rated-user user-orange" href="/profile/star_xingchen_c" title="Master star_xingchen_c">star_xingchen_c</a>, <a class="rated-user user-orange" href="/profile/syksykCCC" title="Master syksykCCC">syksykCCC</a>.</p><p>We would also like to thank:</p> <ul>   <li>Great <a class="rated-user user-red" href="/profile/antontrygubO_o" title="International Grandmaster antontrygubO_o">antontrygubO_o</a> for <strike>rejecting problems</strike> greatest coordination!</li>   <li><a class="rated-user user-red" href="/profile/tfg" title="Grandmaster tfg">tfg</a>, <a class="rated-user user-red" href="/profile/dvdg6566" title="Grandmaster dvdg6566">dvdg6566</a>, <a class="rated-user user-red" href="/profile/cjy2003" title="International Grandmaster cjy2003">cjy2003</a>, <a class="rated-user user-red" href="/profile/Tlatoani" title="International Grandmaster Tlatoani">Tlatoani</a>, <a class="rated-user user-orange" href="/profile/purinliang" title="Master purinliang">purinliang</a>, <a class="rated-user user-violet" href="/profile/user14767553" title="Candidate Master user14767553">user14767553</a>, <a class="rated-user user-orange" href="/profile/bensonlzl" title="Master bensonlzl">bensonlzl</a>, <a class="rated-user user-orange" href="/profile/wlzhouzhuan" title="International Master wlzhouzhuan">wlzhouzhuan</a>, <a class="rated-user user-blue" href="/profile/maomao90" title="Expert maomao90">maomao90</a>, <a class="rated-user user-violet" href="/profile/huangqr" title="Candidate Master huangqr">huangqr</a>, <a class="rated-user user-orange" href="/profile/mollnn" title="Master mollnn">mollnn</a>, <a class="rated-user user-orange" href="/profile/golions" title="Master golions">golions</a>, <a class="rated-user user-cyan" href="/profile/b23v" title="Specialist b23v">b23v</a>, <a class="rated-user user-violet" href="/profile/qlf9" title="Candidate Master qlf9">qlf9</a>, <a class="rated-user user-red" href="/profile/gamegame" title="International Grandmaster gamegame">gamegame</a>, <a class="rated-user user-violet" href="/profile/Myrcella" title="Candidate Master Myrcella">Myrcella</a>, <a class="rated-user user-legendary" href="/profile/kefaa2" title="Legendary Grandmaster kefaa2"><span class="legendary-user-first-letter">k</span>efaa2</a>, <a class="rated-user user-orange" href="/profile/Dormi" title="Master Dormi">Dormi</a>, <a class="rated-user user-orange" href="/profile/flaviu2001" title="Master flaviu2001">flaviu2001</a>, <a class="rated-user user-blue" href="/profile/Karavaiev" title="Expert Karavaiev">Karavaiev</a>, <a class="rated-user user-orange" href="/profile/wh0816" title="Master wh0816">wh0816</a>, <a class="rated-user user-violet" href="/profile/kdjonty31" title="Candidate Master kdjonty31">kdjonty31</a>, <a class="rated-user user-orange" href="/profile/socho" title="International Master socho">socho</a>, <a class="rated-user user-orange" href="/profile/Karry5307" title="International Master Karry5307">Karry5307</a>, <a class="rated-user user-orange" href="/profile/dXqwq" title="Master dXqwq">dXqwq</a>, <a class="rated-user user-red" href="/profile/KagamineRin" title="Grandmaster KagamineRin">KagamineRin</a>, <a class="rated-user user-orange" href="/profile/hzkmd" title="International Master hzkmd">hzkmd</a>, <a class="rated-user user-orange" href="/profile/syh0313" title="Master syh0313">syh0313</a>, <a class="rated-user user-orange" href="/profile/Nanako" title="Master Nanako">Nanako</a>, <a class="rated-user user-red" href="/profile/sunsiyu" title="Grandmaster sunsiyu">sunsiyu</a>, <a class="rated-user user-orange" href="/profile/zkdxl" title="Master zkdxl">zkdxl</a>, <a class="rated-user user-violet" href="/profile/Pecco" title="Candidate Master Pecco">Pecco</a>, <a class="rated-user user-orange" href="/profile/ODT" title="Master ODT">ODT</a> for testing the round and <strike>sticking on difficult problems</strike> providing valuable feedback to the problems.</li>   <li><a class="rated-user user-red" href="/profile/froggyzhang" title="Grandmaster froggyzhang">froggyzhang</a>, <a class="rated-user user-orange" href="/profile/paulzrm" title="Master paulzrm">paulzrm</a>, <a class="rated-user user-blue" href="/profile/mejiamejia" title="Expert mejiamejia">mejiamejia</a> for giving great <strike>rejected</strike> problems and many interesting ideas.</li>   <li><a class="rated-user user-admin" href="/profile/MikeMirzayanov" title="Headquarters, MikeMirzayanov">MikeMirzayanov</a> for great Codeforces and Polygon platforms!</li>   <li>And You, for participating! Your participation in this round will allow <strike>our emperor</strike> Anton to reject more problems.</li> </ul><p>You will have 3 hours to solve 9 problems. We encourage you to read all the problems <strike>and solve them all</strike>.</p><p>One of these problems is interactive, please see <a href="https://codeforces.com/blog/entry/45307">the guide of interactive problems</a> if you are not familiar with it.</p><p><strong>UPD1:</strong> Scoring distribution: 500-750-1000-1250-1750-**************-5000</p><p><strong>UPD2:</strong> <a href="https://codeforces.com/blog/entry/88248">Tutorial</a> published.</p><p><strong>UPD3:</strong> System testing finished, congrats to the winners!</p> <ol>   <li><a class="rated-user user-legendary" href="/profile/maroonrk" title="Legendary Grandmaster maroonrk"><span class="legendary-user-first-letter">m</span>aroonrk</a></li>   <li><a class="rated-user user-legendary" href="/profile/DmitryGrigorev" title="Legendary Grandmaster DmitryGrigorev"><span class="legendary-user-first-letter">D</span>mitryGrigorev</a></li>   <li><a class="rated-user user-legendary" href="/profile/Petr" title="Legendary Grandmaster Petr"><span class="legendary-user-first-letter">P</span>etr</a></li>   <li><a class="rated-user user-legendary" href="/profile/jiangly" title="Legendary Grandmaster jiangly"><span class="legendary-user-first-letter">j</span>iangly</a></li>   <li><a class="rated-user user-red" href="/profile/RALZH" title="International Grandmaster RALZH">RALZH</a></li>   <li><a class="rated-user user-red" href="/profile/qazswedx2" title="International Grandmaster qazswedx2">qazswedx2</a></li>   <li><a class="rated-user user-legendary" href="/profile/sunset" title="Legendary Grandmaster sunset"><span class="legendary-user-first-letter">s</span>unset</a></li>   <li><a class="rated-user user-legendary" href="/profile/ecnerwala" title="Legendary Grandmaster ecnerwala"><span class="legendary-user-first-letter">e</span>cnerwala</a></li>   <li><a class="rated-user user-legendary" href="/profile/lumibons" title="Legendary Grandmaster lumibons"><span class="legendary-user-first-letter">l</span>umibons</a></li>   <li><a class="rated-user user-red" href="/profile/p_b_p_b" title="Grandmaster p_b_p_b">p_b_p_b</a></li> </ol></div><p><a href="/blog/entry/88134">Read more »</a></p>
                    </div>

                    <div style="font-size: 1.1rem;line-height: 1.1rem;padding-bottom: 0.5em;">
                        <img src="//sta.codeforces.com/s/76577/images/icons/paperclip-16x16.png" style="vertical-align: middle;"/> <span style="padding: 0 0.35em;">Announcement of <a href="/contest/1491" class="notice" style="text-decoration: none;">Codeforces Global Round 13</a> </span>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".delete-resource-link-151956-88679").click(function() {
                                var that = this;
                                Codeforces.confirm("Are you sure you want to detach a contest?", function () {
                                    $.post("/data/blogAndContest", {
                                        action: "detachBlogFromContest",
                                        blogId: "151956",
                                        blogEntryId: "88134",
                                        contestId: $(that).attr("data-contestId"),
                                        resourceIds: $(that).attr("data-resourceIds")
                                    }, function(json) {
                                        Codeforces.reloadAndShowMessageOrShowError(json, "Contest detached");
                                    });
                                }, function () {}, "Yes", "No");
                            });
                        });
                    </script>


                    <div style="font-size: 1.1rem;line-height: 11px;">
                        <img style="vertical-align: middle;" src="//sta.codeforces.com/s/76577/images/blog/tags.png" title="Tags" alt="Tags"/>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=codeforces+global+rounds" class="tag notice" style="text-decoration: none;">codeforces global rounds</a>,
                </span>
                        <span style="padding: 0 0.35em;">
    <a href="/search?query=global+round+13" class="tag notice" style="text-decoration: none;">global round 13</a>
                </span>
                    </div>

                    <div class="roundbox meta" style="">
                        <div class="roundbox-lt">&nbsp;</div>
                        <div class="roundbox-rt">&nbsp;</div>
                        <div class="roundbox-lb">&nbsp;</div>
                        <div class="roundbox-rb">&nbsp;</div>
                        <div class="left-meta">
                            <ul>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-up-88679"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/voteup.png"
                                                                                                                     alt="Vote: I like it" title="Vote: I like it"
                                /></a>


                                </li>
                                <li style="line-height: 1.6em;">


                                    <span title="Topic rating" style='font-size:larger;position:relative;bottom:1px;font-weight:bold;color:green'>+1092</span>

                                </li>
                                <li style="line-height: 1.6em;">        <a href="#" class="topic-vote-down-88679"><img style="vertical-align:middle;position:relative;top:-0.2em" src="//sta.codeforces.com/s/76577/images/actions/votedown.png"
                                                                                                                       alt="Vote: I do not like it" title="Vote: I do not like it"
                                /></a>


                                </li>
                            </ul>
                        </div>


                        <span style="position: relative; line-height: 1.65em; top: 0.75rem; left: 0.8em;">
        </span>

                        <div class="right-meta">
                            <ul>
                                <li>        <a href="/profile/star_xingchen_c"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/user_16x16.png"
                                                                                    alt="Author" title="Author"
                                /></a>


                                    <a href="/profile/star_xingchen_c">
                                        star_xingchen_c
                                    </a>
                                </li>
                                <li>        <img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/date_16x16.png"
                                                 alt="Publication date" title="Publication date"
                                />



                                    <span class="format-humantime" title="Feb/23/2021 16:46">3 weeks ago</span>

                                </li>
                                <li>        <a href="/blog/entry/88134#comments"><img style="vertical-align:middle;position:relative;top:-1px" src="//sta.codeforces.com/s/76577/images/blog/comments_16x16.png"
                                                                                      alt="Comments" title="Comments"
                                /></a>


                                    <a href="/blog/entry/88134#comments">
                                        344
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <br style="clear:both;"/>
                    </div>


                    <script type="text/javascript">
                        $(document).ready(function () {
                            $(".topic-vote-up-88679").click(function () {
                                $.post("/data/topic/vote", {topicId: 88679, _tta: Codeforces.tta(), topicRevisionId: 219326, vote: +1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                            $(".topic-vote-down-88679").click(function () {
                                $.post("/data/topic/vote", {topicId: 88679, _tta: Codeforces.tta(), topicRevisionId: 219326, vote: -1}, function(data) {
                                    Codeforces.showMessage(data);
                                }, "json");
                                return false;
                            });
                        });
                    </script>
                </div>
            </div>

            <div class="pagination">
                <ul>
                    <span class="inactive">&larr;</span>
                    <li>
                        <span class="page-index active" pageIndex="1"><a href="/page/1">1</a></span>
                    </li>
                    <li>
                        <span class="page-index" pageIndex="2"><a href="/page/2">2</a></span>
                    </li>
                    <li>
                        <span class="page-index" pageIndex="3"><a href="/page/3">3</a></span>
                    </li>
                    <li>
                        ...
                    </li>
                    <li>
                        <span class="page-index" pageIndex="182"><a href="/page/182">182</a></span>
                    </li>
                    <li>
                        <span class="page-index" pageIndex="183"><a href="/page/183">183</a></span>
                    </li>
                    <li><a href="/page/2" class="arrow">&rarr;</a></li>
                </ul>
            </div>
            <script type="text/javascript">
                $(document).ready(function () {
                    function handlePaginationKeyboardEvent(right) {
                        var pageIndex = parseInt($(".pagination").find("span.active").attr("pageIndex"));
                        if (right) {
                            pageIndex++;
                        } else {
                            pageIndex--;
                        }
                        $(".pagination").find("span[pageIndex=" + pageIndex + "]").each(function () {
                            Codeforces.redirect($(this).find("a").attr("href"));
                        });
                    }

                    $(document).bind('keydown', 'ctrl+left', function() {
                        handlePaginationKeyboardEvent(false);
                    });

                    $(document).bind('keydown', 'ctrl+right', function() {
                        handlePaginationKeyboardEvent(true);
                    });
                });
            </script>                </div>
    </div>
    <br style="clear: both;"/>
    <div id="footer">
        <div><a href="https://codeforces.com/">Codeforces</a> (c) Copyright 2010-2021 Mike Mirzayanov</div>
        <div>The only programming contests Web 2.0 platform</div>
        <div>Server time: <span class="format-timewithseconds" data-locale="en">Mar/19/2021 13:20:47</span> (f1).</div>
        <div>Desktop version, switch to <a rel="nofollow" class="switchToMobile" href="?mobile=true">mobile version</a>.</div>
        <div class="smaller"><a href="/privacy">Privacy Policy</a></div>

        <div style="margin-top: 25px;">
            Supported by
        </div>
        <div style="margin-top: 8px; padding-bottom: 20px; position: relative; left: 10px;">
            <a href="https://telegram.org/"><img style="margin-right: 2em; width: 60px;" src="//sta.codeforces.com/s/76577/images/telegram-100x100.png" alt="Telegram" title="Telegram"/></a>
            <a href="http://ifmo.ru/en/"><img style="width: 120px;" src="//sta.codeforces.com/s/76577/images/itmo_small_en-logo.png" alt="ИТМО" title="ИТМО"/></a>
        </div>
    </div>
    <script type="text/javascript">
        $(function() {
            $(".switchToMobile").click(function() {
                Codeforces.redirect(Codeforces.updateUrlParameter(document.location.href, "mobile", "true"));
                return false;
            });
            $(".switchToDesktop").click(function() {
                Codeforces.redirect(Codeforces.updateUrlParameter(document.location.href, "mobile", "false"));
                return false;
            });
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            if ($(window).width() < 1600) {
                $('.button-up').css('width', '30px').css('line-height', '30px').css('font-size', '20px');
            }

            if ($(window).width() >= 1200) {
                $ (window).scroll (function () {
                    if ($ (this).scrollTop () > 100) {
                        $ ('.button-up').fadeIn();
                    } else {
                        $ ('.button-up').fadeOut();
                    }
                });

                $('.button-up').click(function () {
                    $('body,html').animate({
                        scrollTop: 0
                    }, 500);
                    return false;
                });

                $('.button-up').hover(function () {
                    $(this).animate({
                        'opacity':'1'
                    }).css({'background-color':'#e7ebf0','color':'#6a86a4'});
                }, function () {
                    $(this).animate({
                        'opacity':'0.7'
                    }).css({'background':'none','color':'#d3dbe4'});;
                });
            }
            Codeforces.focusOnError();
        });
    </script>

    <div class="userListsFacebox" style="display:none;">
        <div style="padding: 0.5em; width: 600px; max-height: 200px; overflow-y: auto">
            <div class="datatable"

                 style="background-color: #E1E1E1; padding-bottom: 3px;">
                <div class="lt">&nbsp;</div>
                <div class="rt">&nbsp;</div>
                <div class="lb">&nbsp;</div>
                <div class="rb">&nbsp;</div>

                <div style="padding: 4px 0 0 6px;font-size:1.4rem;position:relative;">
                    User lists

                    <div style="position:absolute;right:0.25em;top:0.35em;">
                        <span style="padding:0;position:relative;bottom:2px;" class="rowCount"></span>

                        <img class="closed" src="//sta.codeforces.com/s/76577/images/icons/control.png"/>

                        <span class="filter" style="display:none;">
                        <img class="opened" src="//sta.codeforces.com/s/76577/images/icons/control-270.png"/>
                        <input style="padding:0 0 0 20px;position:relative;bottom:2px;border:1px solid #aaa;height:17px;font-size:1.3rem;"/>
                    </span>
                    </div>
                </div>
                <div style="background-color: white;margin:0.3em 3px 0 3px;position:relative;">
                    <div class="ilt">&nbsp;</div>
                    <div class="irt">&nbsp;</div>
                    <table class="">
                        <thead>
                        <tr>
                            <th>Name</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
            <script type="text/javascript">
                $(document).ready(function () {
                    // Create new ':containsIgnoreCase' selector for search
                    jQuery.expr[':'].containsIgnoreCase = function(a, i, m) {
                        return jQuery(a).text().toUpperCase()
                            .indexOf(m[3].toUpperCase()) >= 0;
                    };

                    if (window.updateDatatableFilter == undefined) {
                        window.updateDatatableFilter = function(i) {
                            var parent = $(i).parent().parent().parent().parent();
                            $("tr.no-items", parent).remove();
                            $("tr", parent).hide().removeClass('visible');
                            var text = $(i).val();
                            if (text) {
                                $("tr" + ":containsIgnoreCase('" + text + "')", parent).show().addClass('visible');
                            } else {
                                parent.find(".rowCount").text("");
                                $("tr", parent).show().addClass('visible');
                            }

                            var found = false;
                            var visibleRowCount = 0;
                            $("tr", parent).each(function () {
                                if (!found) {
                                    if ($(this).find("th").size() > 0) {
                                        $(this).show().addClass('visible');
                                        found = true;
                                    }
                                }
                                if ($(this).hasClass('visible')) {
                                    visibleRowCount++;
                                }
                            });
                            if (text) {
                                parent.find(".rowCount").text("Matches: " + (visibleRowCount - (found ? 1 : 0)));
                            }
                            if (visibleRowCount == (found ? 1 : 0)) {
                                $("<tr class='no-items visible'><td style=\"text-align:left;\"colspan=\"32\">No items<\/td><\/tr>").appendTo($(parent).find('table'));
                            }
                            $(parent).find("tr td").removeClass("dark");
                            $(parent).find("tr.visible:odd td").addClass("dark");
                        }

                        $(".datatable .closed").click(function () {
                            var parent = $(this).parent();
                            $(this).hide();
                            $(".filter", parent).fadeIn(function () {
                                $("input", parent).val("").focus().css("border", "1px solid #aaa");
                            });
                        });

                        $(".datatable .opened").click(function () {
                            var parent = $(this).parent().parent();
                            $(".filter", parent).fadeOut(function () {
                                $(".closed", parent).show();
                                $("input", parent).val("").each(function () {
                                    window.updateDatatableFilter(this);
                                });
                            });
                        });

                        $(".datatable .filter input").keyup(function(e) {
                            window.updateDatatableFilter(this);
                            e.preventDefault();
                            e.stopPropagation();
                        });

                        $(".datatable table").each(function () {
                            var found = false;
                            $("tr", this).each(function () {
                                if (!found && $(this).find("th").size() == 0) {
                                    found = true;
                                }
                            });
                            if (!found) {
                                $("<tr class='no-items visible'><td style=\"text-align:left;\"colspan=\"32\">No items<\/td><\/tr>").appendTo(this);
                            }
                        });

                        // Applies styles to datatables.
                        $(".datatable").each(function () {
                            $(this).find("tr:first th").addClass("top");
                            $(this).find("tr:last td").addClass("bottom");
                            $(this).find("tr:odd td").addClass("dark");
                            $(this).find("tr td:first-child, tr th:first-child").addClass("left");
                            $(this).find("tr td:last-child, tr th:last-child").addClass("right");
                        });

                        $(".datatable table.tablesorter").each(function () {
                            $(this).bind("sortEnd", function () {
                                $(".datatable").each(function () {
                                    $(this).find("th, td")
                                        .removeClass("top").removeClass("bottom")
                                        .removeClass("left").removeClass("right")
                                        .removeClass("dark");
                                    $(this).find("tr:first th").addClass("top");
                                    $(this).find("tr:last td").addClass("bottom");
                                    $(this).find("tr:odd td").addClass("dark");
                                    $(this).find("tr td:first-child, tr th:first-child").addClass("left");
                                    $(this).find("tr td:last-child, tr th:last-child").addClass("right");
                                });
                            });
                        });
                    }
                });
            </script>
        </div>
    </div>
    <script type="application/javascript">
        $(function() {
            $(".userListMarker").click(function() {
                $.post("/data/lists", {action: "findTouched"}, function(json) {
                    Codeforces.facebox(".userListsFacebox");
                    var tbody = $("#facebox tbody");
                    tbody.empty();
                    for (var i in json) {
                        tbody.append(
                            $("<tr></tr>").append(
                                $("<td></td>").attr("data-readKey", json[i].readKey).text(json[i].name)
                            )
                        );
                    }
                    Codeforces.updateDatatables();
                    tbody.find("td").css("cursor", "pointer").click(function() {
                        document.location = Codeforces.updateUrlParameter(document.location.href, "list", $(this).attr("data-readKey"));
                    });
                }, "json");
            });
        });
    </script>
</div>
<script type="application/javascript">
    if ('serviceWorker' in navigator && 'fetch' in window && 'caches' in window) {
        var parser = new UAParser();
        var browserName = parser.getBrowser().name;
        var browserVersion = parser.getBrowser().version;

        var supportedBrowser = false;
        var supportedBrowsers = {
            "Chrome": "76",
            "Firefox": "68",
            // "Edge": "18",
            "Safari": "12.1",
            "Opera": "63",
            "Yandex": "19.9"
        };

        for (var name in supportedBrowsers) {
            if (name === browserName && supportedBrowsers[name] <= browserVersion) {
                supportedBrowser = true;
            }
        }

        if (supportedBrowser) {
            navigator.serviceWorker.register('/service-worker-76577.js')
                .then(function (registration) {
                    console.log('Service worker registered');
                })
                .catch(function (error) {
                    console.log('Registration failed: ', error);
                });
        } else {
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for (var i = 0; i < registrations.length; i++) {
                    registrations[i].unregister();
                }
            });
        }
    }
</script>
</body>
</html>

