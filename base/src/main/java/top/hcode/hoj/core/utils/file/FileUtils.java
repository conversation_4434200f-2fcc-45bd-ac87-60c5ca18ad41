package top.hcode.hoj.core.utils.file;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件处理工具类
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class FileUtils extends FileUtil {

    public static InputStream getFileInputStream(String fileUrl) {
        InputStream in = null;
        HttpURLConnection conn = null;
        try {
            // 创建URL对象
            URL urlObj = new URL(fileUrl);
            // 打开连接
            conn = (HttpURLConnection) urlObj.openConnection();
            // 设置请求方法（GET）
            conn.setRequestMethod("GET");
            // 设置连接超时
            conn.setConnectTimeout(30000);
            // 设置读取超时
            conn.setReadTimeout(60000);
            // 获取输入流
            in = conn.getInputStream();
        } catch (Exception e) {
            log.error(String.format("解析OSS文件链接异常.... url:%s", fileUrl), e);
        }
        return in;
    }
    /**
     * 下载文件名重新编码
     *
     * @param response     响应对象
     * @param realFileName 真实文件名
     */
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName) {
        String percentEncodedFileName = percentEncode(realFileName);
        // 改用 String.format()
        String contentDispositionValue = String.format(
                "attachment; filename=%s;filename*=utf-8''%s",
                percentEncodedFileName,
                percentEncodedFileName
        );        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
        response.setHeader("Content-disposition", contentDispositionValue);
        response.setHeader("download-filename", percentEncodedFileName);
    }

    /**
     * 百分号编码工具方法
     *
     * @param s 需要百分号编码的字符串
     * @return 百分号编码后的字符串
     */
    @SneakyThrows
    public static String percentEncode(String s) {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.name());
        return encode.replaceAll("\\+", "%20");
    }

    public static MultipartFile toMultipartFile(String url) throws IOException {
        try {
            // 1. 使用 Hutool 下载文件字节（自动处理重定向和流）
            byte[] fileBytes = HttpUtil.downloadBytes(url);

            // 2. 提取文件名（优先从 Content-Disposition 获取）
            String fileName = HttpUtil.createGet(url).execute().header("Content-Disposition");
            fileName = (fileName != null && fileName.contains("filename="))
                       ? fileName.substring(fileName.indexOf("filename=") + 9).replace("\"", "")
                       : FileUtil.getName(new URL(url).getPath());

            // 3. 使用 Apache Commons 创建 MultipartFile
            DiskFileItem fileItem = new DiskFileItem(
                    "file",
                    "application/zip", // 可根据需要动态获取 Content-Type
                    false,
                    fileName,
                    fileBytes.length,
                    null
            );

            try (OutputStream os = fileItem.getOutputStream()) {
                os.write(fileBytes);
            }

            return new CommonsMultipartFile(fileItem);
        } catch (Exception e) {
            throw new IOException("文件下载转换失败: " + e.getMessage(), e);
        }
    }
}
